import {
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useId,
  useImperativeHandle,
  useState,
} from 'react';

import { BlockNoteEditor, locales } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import {
  blockTypeSelectItems,
  DragHandleButton,
  // FormattingToolbar,
  // FormattingToolbarController,
  SideMenu,
  SideMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { debounce } from 'lodash';
import { toast } from 'sonner';

import { archivo } from '@/utils/fonts';

import './styles.scss';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

export type RichTextEditorProps = {
  id?: string;
  defaultValue?: string;
  bg?: 'white' | 'colored';
  maxHeight?: string;
  placeholder?: string;
  children?: ReactNode;
  onChange?: (text: string) => void;
  onImmediateChange?: (document: any) => void;
  onBlur?: () => void;
  className?: string;
  initialBlockType?:
    | 'paragraph'
    | 'bulletListItem'
    | 'numberedListItem'
    | 'checkListItem';
};

export type RichTextEditorRef = {
  editor: BlockNoteEditor;
};

const RichTextEditor = forwardRef(
  (
    {
      id,
      defaultValue = '',
      bg = 'colored',
      maxHeight = '',
      placeholder,
      children,
      onChange = () => {},
      onImmediateChange = () => {},
      onBlur = () => {},
      className,
      initialBlockType = 'paragraph',
    }: RichTextEditorProps,
    ref
  ) => {
    const defaultId = useId();
    const localId = id || defaultId;

    const editor = useCreateBlockNote(
      {
        initialContent: [
          {
            id: 'placeholder-block',
            type: initialBlockType,
            content: '',
          },
        ],
        dictionary: {
          ...locales.en,
          placeholders: {
            ...locales.en.placeholders,
            default: placeholder || 'Enter diagnosis',
          },
        },
      },
      [initialBlockType, placeholder]
    );

    // Simple CSS-based placeholder injection (lightweight, no MutationObserver)
    useEffect(() => {
      if (editor && localId && placeholder) {
        const styleId = `placeholder-${localId.replace(/[^a-zA-Z0-9]/g, '-')}`;
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
          existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = styleId;
        const escapedPlaceholder = placeholder
          .replace(/"/g, '\\"')
          .replace(/'/g, "\\'");
        // Escape special characters in ID for CSS selector
        const escapedId = CSS.escape(localId);
        style.textContent = `
          /* Only show placeholder when editor has ONLY ONE block and it's empty */
          /* This ensures placeholder doesn't show on new empty blocks created after Enter in lists */
          #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty-and-focused="true"] > p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)::before,
          #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty="true"] > p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)::before,
          #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty-and-focused="true"] > p.bn-inline-content:empty::before,
          #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty="true"] > p.bn-inline-content:empty::before {
            content: "${escapedPlaceholder}" !important;
            color: #111827 !important;
            opacity: 0.5 !important;
            pointer-events: none !important;
            display: inline !important;
            padding: 0 !important;
            margin: 0 !important;
            line-height: inherit !important;
            vertical-align: baseline !important;
          }
          /* Hide placeholder on any block that has content */
          #${escapedId} .bn-block-content:not([data-is-empty="true"]):not([data-is-empty-and-focused="true"]) p.bn-inline-content::before {
            content: none !important;
            display: none !important;
          }
          /* Hide placeholder when paragraph has actual text nodes (not just breaks) */
          #${escapedId} p.bn-inline-content:not(:empty):not(:has(br.ProseMirror-trailingBreak:only-child))::before {
            content: none !important;
            display: none !important;
          }
          /* Hide placeholder on blocks that are NOT the first block (subsequent empty blocks after Enter) */
          #${escapedId} .bn-block-group > .bn-block-outer:not(:first-child) .bn-block-content p.bn-inline-content::before {
            content: none !important;
            display: none !important;
          }
          /* Hide placeholder when there are multiple blocks (editor has content) */
          #${escapedId} .bn-block-group:has(.bn-block-outer:nth-child(2)) .bn-block-content p.bn-inline-content::before {
            content: none !important;
            display: none !important;
          }
        `;
        document.head.appendChild(style);

        return () => {
          const styleToRemove = document.getElementById(styleId);
          if (styleToRemove) {
            styleToRemove.remove();
          }
        };
      }
    }, [editor, localId, placeholder]);

    const allowedItems = [
      'paragraph',
      'bulletListItem',
      'numberedListItem',
      'checkListItem',
    ];
    const [blockTypeOptions] = useState(
      blockTypeSelectItems(editor.dictionary).filter((item) =>
        allowedItems.includes(item.type)
      )
    );

    const emitHTML = async () => {
      try {
        const htmlText = await editor.blocksToFullHTML(editor.document);

        // Clean up the HTML to remove nested empty structures
        const cleanedHTML = cleanupBlockNoteHTML(htmlText);
        onChange(cleanedHTML);
      } catch {
        onChange('');
        toast.error('Error processing text input');
      }
    };

    const flattenNestedListBlocks = (blocks: any[]): any[] => {
      return blocks;
    };

    const normalizeHTMLForParsing = (html: string): string => {
      if (!html) return '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      const allUlElements = Array.from(tempDiv.querySelectorAll('ul'));

      allUlElements.forEach((ul) => {
        const nestedUls = ul.querySelectorAll('li > ul');
        nestedUls.forEach((nestedUl) => {
          const parentLi = nestedUl.parentElement;
          if (parentLi && parentLi.tagName === 'LI') {
            const nestedLis = Array.from(nestedUl.querySelectorAll('li'));
            nestedLis.forEach((nestedLi) => {
              if (parentLi.nextSibling) {
                parentLi.parentNode?.insertBefore(
                  nestedLi,
                  parentLi.nextSibling
                );
              } else {
                parentLi.parentNode?.appendChild(nestedLi);
              }
            });

            nestedUl.remove();

            if (
              !parentLi.textContent?.trim() &&
              parentLi.children.length === 0
            ) {
              parentLi.remove();
            }
          }
        });
      });

      const remainingUls = Array.from(tempDiv.querySelectorAll('ul'));
      if (remainingUls.length > 1) {
        let previousUl: HTMLUListElement | null = null;
        remainingUls.forEach((ul) => {
          const hasListItems = ul.querySelectorAll('li').length > 0;

          if (hasListItems) {
            if (previousUl && previousUl.nextSibling === ul) {
              while (ul.firstChild) {
                previousUl.appendChild(ul.firstChild);
              }
              ul.remove();
            } else {
              previousUl = ul;
            }
          } else {
            ul.remove();
          }
        });
      }

      const finalUls = tempDiv.querySelectorAll('ul');
      finalUls.forEach((ul) => {
        const emptyLis = ul.querySelectorAll('li:empty');
        emptyLis.forEach((li) => li.remove());

        if (ul.children.length === 0) {
          ul.remove();
        }
      });

      const listItemBlockOuters = Array.from(
        tempDiv.querySelectorAll(
          '.bn-block-outer:has([data-content-type="bulletListItem"]), .bn-block-outer:has([data-content-type="numberedListItem"])'
        )
      );

      listItemBlockOuters.forEach((listItemOuter) => {
        const nestedGroups = listItemOuter.querySelectorAll('.bn-block-group');

        nestedGroups.forEach((nestedGroup) => {
          const nestedListItems = nestedGroup.querySelectorAll(
            '.bn-block-outer:has([data-content-type="bulletListItem"]), .bn-block-outer:has([data-content-type="numberedListItem"])'
          );

          if (nestedListItems.length > 0) {
            const parentBlockGroup = listItemOuter.closest('.bn-block-group');

            if (parentBlockGroup) {
              nestedListItems.forEach((nestedItem) => {
                parentBlockGroup.insertBefore(
                  nestedItem,
                  listItemOuter.nextSibling
                );
              });

              nestedGroup.remove();

              const listItemContent =
                listItemOuter.querySelector('.bn-block-content');
              if (listItemContent) {
                const emptyParagraphs = listItemContent.querySelectorAll(
                  'p.bn-inline-content:empty, p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)'
                );
                emptyParagraphs.forEach((p) => p.remove());

                if (
                  !listItemContent.textContent?.trim() &&
                  listItemContent.querySelectorAll('.bn-block-group').length ===
                    0
                ) {
                  listItemOuter.remove();
                }
              }
            }
          } else {
            nestedGroup.remove();
          }
        });
      });

      const allBlockGroups = tempDiv.querySelectorAll('.bn-block-group');
      allBlockGroups.forEach((group) => {
        const children = Array.from(group.children);
        children.forEach((child) => {
          if (child.classList.contains('bn-block-outer')) {
            const isListItem = child.querySelector(
              '[data-content-type="bulletListItem"], [data-content-type="numberedListItem"]'
            );
            const isEmpty =
              !child.textContent?.trim() ||
              (child.querySelector(
                'p.bn-inline-content:empty, p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)'
              ) &&
                !isListItem);

            if (isEmpty && !isListItem) {
              child.remove();
            }
          }
        });
      });

      return tempDiv.innerHTML;
    };

    // Function to clean up BlockNote HTML output
    const cleanupBlockNoteHTML = (html: string): string => {
      if (!html) return '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      const allElements = tempDiv.querySelectorAll('*');
      allElements.forEach((el) => {
        const style = el.getAttribute('style');
        if (style) {
          const textAlignMatch = style.match(/text-align:\s*([^;]+)/);
          if (textAlignMatch) {
            const alignment = textAlignMatch[1].trim();
            el.setAttribute('style', `text-align: ${alignment}`);
            // Also set data-text-alignment attribute for consistency
            if (el.classList.contains('bn-block-content')) {
              el.setAttribute('data-text-alignment', alignment);
            }
          } else {
            el.removeAttribute('style');
          }
        }

        const dataAlignment = el.getAttribute('data-text-alignment');
        if (dataAlignment) {
          // Keep the data attribute
        }
      });

      // Remove only clearly empty elements to avoid performance issues
      const emptyParagraphs = tempDiv.querySelectorAll(
        'p.bn-inline-content:empty'
      );
      emptyParagraphs.forEach((p) => p.remove());

      const emptyBlockContent = tempDiv.querySelectorAll(
        '.bn-block-content:empty'
      );
      emptyBlockContent.forEach((div) => div.remove());

      const emptyBlocks = tempDiv.querySelectorAll('.bn-block:empty');
      emptyBlocks.forEach((div) => div.remove());

      const emptyBlockOuters = tempDiv.querySelectorAll(
        '.bn-block-outer:empty'
      );
      emptyBlockOuters.forEach((div) => div.remove());

      return tempDiv.innerHTML;
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedHandleChange = useCallback(debounce(emitHTML, 100), [
      editor,
    ]);

    const handleChange = () => {
      debouncedHandleChange();
      // Only call onImmediateChange if it exists to avoid unnecessary calls
      if (onImmediateChange) {
        onImmediateChange(editor.document);
      }
    };

    useImperativeHandle(ref, () => {
      return {
        editor,
      };
    }, [editor]);

    // Load content only once when component mounts with defaultValue
    useEffect(() => {
      if (defaultValue && defaultValue.trim() !== '') {
        const loadContent = async () => {
          try {
            // Check if editor already has meaningful content to avoid overwriting
            const currentBlocks = editor.document;
            const hasExistingContent = currentBlocks.some(
              (block) =>
                block.content &&
                Array.isArray(block.content) &&
                block.content.length > 0
            );

            if (!hasExistingContent) {
              const normalizedHTML = normalizeHTMLForParsing(defaultValue);

              const blocks = await editor.tryParseHTMLToBlocks(normalizedHTML);
              if (blocks && blocks.length > 0) {
                const flattenedBlocks = flattenNestedListBlocks(blocks);

                const editorContainer = document.getElementById(localId);
                const editorElement = editorContainer?.querySelector(
                  '.bn-editor'
                ) as HTMLElement;
                const containerElement = editorContainer as HTMLElement;

                if (containerElement) {
                  containerElement.style.display = 'none';
                }
                if (editorElement) {
                  editorElement.classList.add('bn-cleaning-up');
                }

                editor.replaceBlocks(editor.document, flattenedBlocks);

                const cleanupNestedStructures = (): boolean => {
                  if (!editorContainer) return false;

                  const hasNestedStructures = editorContainer.querySelector(
                    '.bn-block-outer:has([data-content-type="bulletListItem"]) .bn-block-group .bn-block-outer:has([data-content-type="bulletListItem"]), .bn-block-outer:has([data-content-type="numberedListItem"]) .bn-block-group .bn-block-outer:has([data-content-type="numberedListItem"])'
                  );

                  if (!hasNestedStructures) {
                    return false;
                  }

                  const itemsToMove: {
                    item: Element;
                    parent: Element;
                    nextSibling: Node | null;
                  }[] = [];
                  const itemsToRemove: Element[] = [];

                  const listItemOuters = Array.from(
                    editorContainer.querySelectorAll(
                      '.bn-block-outer:has([data-content-type="bulletListItem"]), .bn-block-outer:has([data-content-type="numberedListItem"])'
                    )
                  );

                  listItemOuters.forEach((listItemOuter) => {
                    const nestedGroups =
                      listItemOuter.querySelectorAll('.bn-block-group');

                    nestedGroups.forEach((nestedGroup) => {
                      const nestedListItems = Array.from(
                        nestedGroup.querySelectorAll(
                          '.bn-block-outer:has([data-content-type="bulletListItem"]), .bn-block-outer:has([data-content-type="numberedListItem"])'
                        )
                      );

                      if (nestedListItems.length > 0) {
                        const parentBlockGroup =
                          listItemOuter.closest('.bn-block-group');

                        if (parentBlockGroup) {
                          nestedListItems.forEach((nestedItem) => {
                            itemsToMove.push({
                              item: nestedItem,
                              parent: parentBlockGroup,
                              nextSibling: listItemOuter.nextSibling,
                            });
                          });

                          itemsToRemove.push(nestedGroup);

                          const listItemContent =
                            listItemOuter.querySelector('.bn-block-content');
                          if (listItemContent) {
                            const emptyParagraphs =
                              listItemContent.querySelectorAll(
                                'p.bn-inline-content:empty, p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)'
                              );
                            emptyParagraphs.forEach((p) =>
                              itemsToRemove.push(p)
                            );

                            if (
                              !listItemContent.textContent?.trim() &&
                              listItemContent.querySelectorAll(
                                '.bn-block-group'
                              ).length === 0
                            ) {
                              itemsToRemove.push(listItemOuter);
                            }
                          }
                        }
                      } else {
                        itemsToRemove.push(nestedGroup);
                      }
                    });
                  });

                  // Execute all DOM operations
                  itemsToMove.forEach(({ item, parent, nextSibling }) => {
                    parent.insertBefore(item, nextSibling);
                  });

                  itemsToRemove.forEach((item) => {
                    item.remove();
                  });

                  return true;
                };

                const showContainerAfterCleanup = () => {
                  let attempts = 0;
                  const maxAttempts = 20;

                  const performCleanup = () => {
                    const needsMoreCleanup = cleanupNestedStructures();
                    attempts++;

                    if (needsMoreCleanup && attempts < maxAttempts) {
                      performCleanup();
                    } else {
                      if (containerElement) {
                        containerElement.style.display = '';
                      }
                      if (editorElement) {
                        editorElement.classList.remove('bn-cleaning-up');
                      }
                    }
                  };

                  if (editorContainer) {
                    const observer = new MutationObserver((mutations, obs) => {
                      obs.disconnect();

                      performCleanup();
                    });

                    observer.observe(editorContainer, {
                      childList: true,
                      subtree: true,
                    });

                    setTimeout(() => {
                      observer.disconnect();
                      performCleanup();
                    }, 50);
                  } else {
                    performCleanup();
                  }
                };

                showContainerAfterCleanup();

                // After inserting, try to apply alignment by manipulating the DOM directly
                setTimeout(() => {
                  const tempDiv = document.createElement('div');
                  tempDiv.innerHTML = defaultValue;

                  const alignmentElements = tempDiv.querySelectorAll(
                    '[data-text-alignment]'
                  );

                  // Use getElementById instead of querySelector to avoid selector issues
                  const editorContainer = document.getElementById(localId);
                  const editorElement =
                    editorContainer?.querySelector('.bn-editor');

                  if (editorElement && alignmentElements.length > 0) {
                    const blockContents =
                      editorElement.querySelectorAll('.bn-block-content');

                    alignmentElements.forEach((alignEl, index) => {
                      const alignment = alignEl.getAttribute(
                        'data-text-alignment'
                      );
                      if (alignment && blockContents[index]) {
                        const blockContent = blockContents[
                          index
                        ] as HTMLElement;
                        blockContent.setAttribute(
                          'data-text-alignment',
                          alignment
                        );
                        blockContent.style.textAlign = alignment;

                        // Also apply to inline content
                        const inlineContent = blockContent.querySelector(
                          '.bn-inline-content'
                        ) as HTMLElement;
                        if (inlineContent) {
                          inlineContent.style.textAlign = alignment;
                        }
                      }
                    });
                  }
                }, 100);
              }
            }
          } catch (error) {
            console.error('Error loading initial content:', error);
          }
        };

        // Small delay to ensure editor is fully initialized
        setTimeout(loadContent, 50);
      }
      // Only run on mount, not when defaultValue changes to prevent loops
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle click to focus the editor and position cursor correctly
    useEffect(() => {
      if (editor && localId) {
        const editorElement = document.getElementById(localId);
        if (!editorElement) return;

        const handleClick = (e: MouseEvent) => {
          const target = e.target as HTMLElement;
          // Only handle clicks on the editor container or its children (not on buttons, etc.)
          if (
            editorElement.contains(target) &&
            !target.closest('button') &&
            !target.closest('[role="button"]') &&
            !target.closest('.bn-toolbar') &&
            !target.closest('.bn-side-menu')
          ) {
            // Use double requestAnimationFrame to ensure this runs after BlockNote's handlers
            requestAnimationFrame(() => {
              requestAnimationFrame(() => {
                const proseMirror = editorElement.querySelector(
                  '.ProseMirror'
                ) as HTMLElement;
                if (proseMirror) {
                  // Always focus the editor first
                  proseMirror.focus();

                  // Check if clicking on a block container (not directly on text)
                  const clickedBlock = target.closest('.bn-block-outer');
                  if (clickedBlock && !target.closest('p.bn-inline-content')) {
                    try {
                      const blockId = clickedBlock.getAttribute('data-id');
                      if (blockId) {
                        const block = editor.document.find(
                          (b) => b.id === blockId
                        );
                        if (block) {
                          const hasContent =
                            block.content &&
                            Array.isArray(block.content) &&
                            block.content.length > 0 &&
                            block.content.some(
                              (item: any) =>
                                item.type === 'text' &&
                                item.text &&
                                item.text.trim().length > 0
                            );

                          if (hasContent) {
                            editor.setTextCursorPosition(block, 'end');
                          } else {
                            // Position cursor at the START if block is empty - this ensures cursor is visible
                            editor.setTextCursorPosition(block, 'start');
                            // Hide placeholder when clicking on empty block
                            hidePlaceholder();
                          }

                          setTimeout(() => {
                            proseMirror.focus();
                          }, 0);
                        }
                      }
                    } catch (error) {
                      proseMirror.focus();
                    }
                  } else {
                    // Hide placeholder when clicking directly on text content
                    hidePlaceholder();
                    proseMirror.focus();
                  }
                }
              });
            });
          }
        };

        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Enter' && editorElement.contains(e.target as Node)) {
            requestAnimationFrame(() => {
              requestAnimationFrame(() => {
                const proseMirror = editorElement.querySelector(
                  '.ProseMirror'
                ) as HTMLElement;
                if (proseMirror) {
                  if (document.activeElement !== proseMirror) {
                    proseMirror.focus();
                  }

                  try {
                    const currentBlock = editor.getTextCursorPosition().block;
                    if (currentBlock) {
                      editor.setTextCursorPosition(currentBlock, 'start');

                      setTimeout(() => {
                        proseMirror.focus();
                      }, 0);
                    }
                  } catch (error) {
                    proseMirror.focus();
                  }
                }
              });
            });
          }
        };

        const hidePlaceholder = () => {
          // Set data attributes to prevent placeholder logic
          const inlineContents = editorElement.querySelectorAll(
            'p.bn-inline-content'
          );
          inlineContents.forEach((content) => {
            content.setAttribute('data-is-empty-and-focused', 'false');
            content.setAttribute('data-is-empty', 'false');
          });

          // Override the placeholder in the original CSS without removing it
          const originalStyleId = `placeholder-${localId.replace(/[^a-zA-Z0-9]/g, '-')}`;
          const originalStyle = document.getElementById(originalStyleId);
          if (originalStyle) {
            const overrideCSS = `
              #${CSS.escape(localId)} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty-and-focused="true"] > p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)::before,
              #${CSS.escape(localId)} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty="true"] > p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)::before,
              #${CSS.escape(localId)} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty-and-focused="true"] > p.bn-inline-content:empty::before,
              #${CSS.escape(localId)} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty="true"] > p.bn-inline-content:empty::before {
                content: none !important;
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
              }
            `;
            originalStyle.textContent += overrideCSS;
          }
        };

        const handleBlur = () => {
          // Small delay to ensure blur has completed and check if editor is empty
          setTimeout(() => {
            const currentBlocks = editor.document;
            const hasContent = currentBlocks.some(
              (block) =>
                block.content &&
                Array.isArray(block.content) &&
                block.content.length > 0 &&
                block.content.some(
                  (item: any) =>
                    item.type === 'text' &&
                    item.text &&
                    item.text.trim().length > 0
                )
            );

            if (!hasContent) {
              // Restore placeholder by resetting data attributes for this specific editor
              const inlineContents = editorElement.querySelectorAll(
                'p.bn-inline-content'
              );
              inlineContents.forEach((content) => {
                content.removeAttribute('data-is-empty-and-focused');
                content.removeAttribute('data-is-empty');
              });

              // Also remove the override CSS to restore original placeholder behavior
              const originalStyleId = `placeholder-${localId.replace(/[^a-zA-Z0-9]/g, '-')}`;
              const originalStyle = document.getElementById(originalStyleId);
              if (originalStyle) {
                // Remove the override CSS by resetting to original content
                const escapedPlaceholder = placeholder
                  ? placeholder.replace(/"/g, '\\"').replace(/'/g, "\\'")
                  : 'Enter diagnosis';
                const escapedId = CSS.escape(localId);
                originalStyle.textContent = `
                  /* Only show placeholder when editor has ONLY ONE block and it's empty */
                  /* This ensures placeholder doesn't show on new empty blocks created after Enter in lists */
                  #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty-and-focused="true"] > p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)::before,
                  #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty="true"] > p.bn-inline-content:has(br.ProseMirror-trailingBreak:only-child)::before,
                  #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty-and-focused="true"] > p.bn-inline-content:empty::before,
                  #${escapedId} .bn-block-group:has(.bn-block-outer:only-child) > .bn-block-outer:first-child .bn-block-content[data-is-empty="true"] > p.bn-inline-content:empty::before {
                    content: "${escapedPlaceholder}" !important;
                    color: #111827 !important;
                    opacity: 0.5 !important;
                    pointer-events: none !important;
                    display: inline !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    line-height: inherit !important;
                    vertical-align: baseline !important;
                  }
                  /* Hide placeholder on any block that has content */
                  #${escapedId} .bn-block-content:not([data-is-empty="true"]):not([data-is-empty-and-focused="true"]) p.bn-inline-content::before {
                    content: none !important;
                    display: none !important;
                  }
                  /* Hide placeholder when paragraph has actual text nodes (not just breaks) */
                  #${escapedId} p.bn-inline-content:not(:empty):not(:has(br.ProseMirror-trailingBreak:only-child))::before {
                    content: none !important;
                    display: none !important;
                  }
                  /* Hide placeholder on blocks that are NOT the first block (subsequent empty blocks after Enter) */
                  #${escapedId} .bn-block-group > .bn-block-outer:not(:first-child) .bn-block-content p.bn-inline-content::before {
                    content: none !important;
                    display: none !important;
                  }
                  /* Hide placeholder when there are multiple blocks (editor has content) */
                  #${escapedId} .bn-block-group:has(.bn-block-outer:nth-child(2)) .bn-block-content p.bn-inline-content::before {
                    content: none !important;
                    display: none !important;
                  }
                `;
              }
            }
          }, 50);
        };

        editorElement.addEventListener('mousedown', handleClick, true);
        editorElement.addEventListener('keydown', handleKeyDown, true);
        editorElement.addEventListener('blur', handleBlur, true);
        return () => {
          editorElement.removeEventListener('mousedown', handleClick, true);
          editorElement.removeEventListener('keydown', handleKeyDown, true);
          editorElement.removeEventListener('blur', handleBlur, true);
        };
      }
    }, [editor, localId]);

    return (
      <BlockNoteView
        id={localId}
        className={`
        rich-text-editor text-sm 
        ${bg} 
        ${className}
       
      `}
        style={{
          ...archivo.style,
          ...(maxHeight
            ? {
                maxHeight,
                overflow: 'auto',
              }
            : {}),
        }}
        editor={editor}
        sideMenu={false}
        slashMenu={false}
        formattingToolbar={true}
        onChange={handleChange}
        onBlur={onBlur}
      >
        <SideMenuController
          sideMenu={(props) => (
            <SideMenu {...props}>
              <DragHandleButton {...props} />
            </SideMenu>
          )}
        />
        {/* Temporarily using default formatting toolbar */}
        {/* <FormattingToolbarController
          formattingToolbar={() => (
            <FormattingToolbar blockTypeSelectItems={blockTypeOptions} />
          )}
        /> */}

        {children}
      </BlockNoteView>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

// Remove memo to ensure placeholder updates work correctly
export default RichTextEditor;
