'use client';

import { useEffect, useState } from 'react';

import { useForm } from 'react-hook-form';

import { maxBy } from 'lodash';
import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiSave, BiX } from 'react-icons/bi';
import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import {
  EmrTypes,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useAmbientStore } from '@/store/emr/lifestyle/ambient-listening/ambient-store';

import {
  getSpeechToken,
  SummarizeConversationRes,
  summarizeConversation,
  summarizeAmbientListening,
  retryAmbientListening,
  retryConsultationSummary,
} from '@/query/speech';

import { CurrentModal, currentModal } from '@/constants/ambient-listening';

import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import SummaryForm from '@/emr/components/consultation/summary-form';
import Transcript from '@/emr/components/transcript';

import './styles.scss';

import PrimaryButton from '@/core/components/primary-button';

import Loading from '../common/loading';
import RecordConsultation from '../modals/RecordConsultation';

import { useUserStore } from '@/store/userStore';

import AppModal from '@/core/components/app-modal';
import API_CONFIG from '@/core/configs/api';

const { SPEECH_REGION } = API_CONFIG;

const RecordingLanguage = {
  English: 'en-IN',
  Malayalam: 'ml-IN',
  Tamil: 'ta-IN',
  Kannada: 'kn-IN',
  Telugu: 'te-IN',
  Bengali: 'bn-IN',
  Hindi: 'hi-IN',
} as const;

type LanguageType = (typeof RecordingLanguage)[keyof typeof RecordingLanguage];

const {
  INITIAL,
  RECORD_CONSULTATION,
  LOADING,
  SHOW_SUMMARY,
  LANGUAGE_SELECTION,
} = currentModal;

export interface AddRecordProps {
  onSave?: (data: SummarizeConversationRes['summary']) => void;
  disabled?: boolean;
  isLoading?: boolean;
  summaryFormTitle?: string;
  summaryFormSubtitle?: string;
  modalTitle?: string;
  customSummaryForm?: React.ReactNode;
  isAmbientRecord?: boolean;
  source?: string;
}

const AddRecord = ({
  onSave = () => {},
  disabled = false,
  isLoading = false,
  summaryFormTitle = 'Consultation',
  modalTitle = '',
  customSummaryForm,
  isAmbientRecord = false,
  source = '',
}: AddRecordProps) => {
  const { doctorProfile } = useDoctorStore();

  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { patient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();

  const customEmrData = maxBy<EmrTypes>(
    customiseEmrData,
    (item) => new Date(item.created_on as string)
  );

  const emrLanguage = customEmrData?.preferred_language_for_ambient_listening;

  const doctorLanguage = Object.entries(RecordingLanguage).find(
    ([key]) => key === emrLanguage
  )?.[1];

  const [engine, setEngine] =
    useState<speechsdk.ConversationTranscriber | null>(null);
  const [response, setResponse] = useState<SummarizeConversationRes | null>(
    null
  );
  const [currentMode, setCurrentMode] = useState<CurrentModal>(INITIAL);
  const [language, setLanguage] = useState<LanguageType>(
    RecordingLanguage.English
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEngineInitializing, setIsEngineInitializing] = useState(false);
  const [retryLoading, setRetryLoading] = useState(false);

  const form = useForm<SummarizeConversationRes['summary']>();

  const startFlow = () => {
    setCurrentMode(LANGUAGE_SELECTION);
    setIsModalOpen(true);
  };

  const handleClose = () => {
    if (isLoading) return; // Prevent closing while saving

    // Enhanced cleanup to prevent stuck states
    try {
      if (engine) {
        // Stop any ongoing transcription
        engine.stopTranscribingAsync(
          () => console.log('Transcription stopped'),
          () => console.log('Transcription stop failed')
        );

        // Close the engine to free resources
        engine.close();
      }
    } catch (error) {
      console.error('Error during engine cleanup:', error);
    }

    setCurrentMode(INITIAL);
    setLanguage(doctorLanguage || RecordingLanguage.English);
    setEngine(null); // Reset engine so it gets reinitialized with default language on next open
    setIsModalOpen(false);
  };

  const initializeSpeechEngine = async (language: string) => {
    try {
      setIsEngineInitializing(true);

      // Add timeout for speech token request to prevent hanging
      const tokenPromise = getSpeechToken();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error('Speech token request timeout')),
          10000
        )
      );

      const speechToken = await Promise.race([tokenPromise, timeoutPromise]);

      if (!speechToken) {
        throw new Error('Failed to get speech token');
      }

      const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
        speechToken,
        SPEECH_REGION
      );
      speechConfig.speechRecognitionLanguage = language;

      const audioConfig = speechsdk.AudioConfig.fromDefaultMicrophoneInput();
      const recognizer = new speechsdk.ConversationTranscriber(
        speechConfig,
        audioConfig
      );

      // Store reference for cleanup
      setEngine(recognizer);
      return recognizer;
    } catch (error) {
      console.error('Error initializing speech engine:', error);
      toast.error('Failed to initialize speech recognition. Please try again.');
      throw error;
    } finally {
      setIsEngineInitializing(false);
    }
  };

  const handleChooseLanguage = async (newLanguage: string) => {
    setLanguage(newLanguage as any);
    setCurrentMode(LOADING);
    try {
      await initializeSpeechEngine(newLanguage);
      setCurrentMode(LANGUAGE_SELECTION);
    } catch (error) {
      console.error('Error initializing speech engine:', error);
      toast.error('Failed to initialize speech engine. Please try again.');
      setCurrentMode(LANGUAGE_SELECTION);
    }
  };

  const {
    setConversation,
    setSummary,
    setLoading,
    isLoading: ambientLoading,
  } = useAmbientStore();

  const handleTranscript = async (transcript: string, duration: number) => {
    try {
      setCurrentMode(LOADING);
      setLoading(true);

      if (isAmbientRecord && source) {
        // Validate that source is not empty or null
        if (!source || source.trim() === '') {
          console.error('Invalid source for ambient listening:', source);
          toast.error(
            'Please navigate to a lifestyle category first before using ambient listening'
          );
          throw new Error('Invalid source for ambient listening');
        }

        // Validate that transcript is not empty
        if (!transcript || transcript.trim() === '') {
          console.error(
            'Empty transcript for ambient listening. Engine ready:',
            !!engine
          );
          toast.error(
            'No audio transcript available. Please try recording again.'
          );
          throw new Error('Empty transcript for ambient listening');
        }

        try {
          const ambientResponse = await summarizeAmbientListening(
            source,
            transcript,
            userData?.id,
            patient?.id
          );

          // Update the ambient store with the response
          if (ambientResponse.conversation) {
            setConversation(ambientResponse.conversation);
          }
          if (ambientResponse.summary) {
            setSummary({
              ...ambientResponse.summary,
              recordingDuration: duration,
            });
          }

          // Also update local state if needed
          setResponse({
            conversation: ambientResponse.conversation || [],
            summary: {
              ...ambientResponse.summary,
              recordingDuration: duration,
            },
          });
        } catch (error) {
          console.error('Error in ambient listening:', error);
          toast.error('Error processing ambient data');
          throw error;
        } finally {
          setLoading(false);
        }
      } else {
        try {
          const consultationResponse = await summarizeConversation(
            transcript,
            userData?.id,
            patient?.id
          );
          setResponse({
            ...consultationResponse,
            summary: {
              ...consultationResponse.summary,
              recordingDuration: duration,
            },
          });
        } catch (error) {
          console.error('Error in conversation summary:', error);
          throw error;
        } finally {
          setLoading(false);
        }
      }

      setCurrentMode(SHOW_SUMMARY);
    } catch (error) {
      if (!isAmbientRecord) {
        toast.error('Error in summarizing conversation');
      }
      setCurrentMode(LANGUAGE_SELECTION);
      setResponse(null);
      console.error('Error in handleTranscript:', error);
      setLoading(false);
    }
  };

  // Create a function to handle the save operation
  const handleSaveOperation = async () => {
    // If a custom summary form (lifestyle modal) is provided by the caller
    // we should delegate the save to that form instead of using this
    // internal form. The caller (AmbientRecords) registers a saveRef on
    // the custom form which will submit the visible form instance with
    // the user's latest manual edits. Calling onSave() without data
    // signals the parent to trigger the registered saveRef.
    if (customSummaryForm && isAmbientRecord) {
      // Call onSave with undefined payload so the parent (AmbientRecords)
      // can trigger the registered saveRef on the visible custom form.
      if (onSave) {
        await onSave(undefined as any);
      }
      return;
    }

    await form.handleSubmit(async (formData) => {
      // Get current form state
      const currentFormState = form.getValues();

      // Deep merge strategy for nested objects
      const mergeNestedObjects = (base: any, update: any) => {
        const merged = { ...base };
        Object.keys(update).forEach((key) => {
          if (
            update[key] !== null &&
            typeof update[key] === 'object' &&
            !Array.isArray(update[key])
          ) {
            merged[key] = mergeNestedObjects(base[key] || {}, update[key]);
          } else if (update[key] !== undefined) {
            // Only update if the new value is not undefined
            merged[key] = update[key];
          }
        });
        return merged;
      };

      // For ambient records
      if (isAmbientRecord && source) {
        const completeData = {
          ...mergeNestedObjects(response?.summary || {}, currentFormState),
          ...(response?.conversation && {
            conversation: response.conversation,
          }),
          recordingDuration: response?.summary?.recordingDuration,
        };

        // Update form with merged data to ensure consistency
        form.reset(completeData);
        await onSave(completeData);
      } else {
        // For non-ambient records
        const completeData = {
          ...mergeNestedObjects(response?.summary || {}, currentFormState),
          ...(response?.conversation && {
            conversation: response.conversation,
          }),
          recordingDuration: response?.summary?.recordingDuration,
        };
        await onSave(completeData);
      }
    })();
  };

  // The save handler that will be called when the save button is clicked
  const handleSave = async () => {
    try {
      await handleSaveOperation();
      // Only close the modal if save was successful
      handleClose();
    } catch (error) {
      console.error('Error saving record:', error);
      const errorMessage =
        isAmbientRecord && source
          ? 'Error saving ambient record'
          : 'Error saving record';
      toast.error(errorMessage);
      // Don't close the modal on error
    }
  };

  // Retry handler for ambient listening
  const handleRetry = async () => {
    if (!userData?.id || !patient?.id) {
      toast.error('User or patient information not available');
      return;
    }

    try {
      setRetryLoading(true);
      setLoading(true); // Show loading in tabs content

      let retryResponse;
      if (isAmbientRecord && source) {
        // For lifestyle ambient listening
        retryResponse = await retryAmbientListening(
          userData.id,
          patient.id,
          source
        );
      } else {
        // For consultation
        retryResponse = await retryConsultationSummary(userData.id, patient.id);
      }

      // Normalize retry response field names to match expected format
      const normalizeRetryResponse = (retryData: any) => {
        if (!retryData.summary) return retryData;

        const normalizedSummary = { ...retryData.summary };

        // Map lowercase/different field names to expected format
        const fieldMappings = {
          presentingcomplaint: 'presentingComplaints',
          historyofpresenting: 'historyOfPresenting',
          pastmedicalhistory: 'pastMedicalHistory',
          pastsurgicalhistory: 'pastSurgicalHistory',
          familyhistory: 'familyHistory',
          addictionhistory: 'addictionHistory',
          currentmedicationhistory: 'currentMedicationHistory',
          diethistory: 'dietHistory',
          sleephistory: 'sleepHistory',
          stresshistory: 'stressHistory',
          physicalactivityhistory: 'physicalActivityHistory',
          generalphysicalexamination: 'generalPhysicalExamination',
          systemicexamination: 'systemicExamination',
        };

        // Apply field mappings
        Object.entries(fieldMappings).forEach(([oldKey, newKey]) => {
          if (normalizedSummary[oldKey] !== undefined) {
            normalizedSummary[newKey] = normalizedSummary[oldKey];
            delete normalizedSummary[oldKey];
          }
        });

        return {
          ...retryData,
          summary: normalizedSummary,
        };
      };

      const normalizedRetryResponse = normalizeRetryResponse(retryResponse);

      // Update the ambient store with the retry response (for lifestyle)
      if (isAmbientRecord && normalizedRetryResponse.conversation) {
        setConversation(normalizedRetryResponse.conversation);
      }
      if (isAmbientRecord && normalizedRetryResponse.summary) {
        setSummary({
          ...normalizedRetryResponse.summary,
          recordingDuration:
            (normalizedRetryResponse.summary as any).recordingDuration ||
            response?.summary?.recordingDuration,
        });
      }

      // Update local state for both consultation and lifestyle
      const newResponse = {
        conversation:
          normalizedRetryResponse.conversation || response?.conversation || [],
        summary: {
          ...normalizedRetryResponse.summary,
          recordingDuration:
            (normalizedRetryResponse.summary as any).recordingDuration ||
            response?.summary?.recordingDuration,
        },
      };

      setResponse(newResponse);

      // Explicitly reset the form with the new summary data for retry
      if (newResponse.summary) {
        setTimeout(() => {
          form.reset(newResponse.summary);
        }, 100); // Small delay to ensure state update completes
      }

      toast.success('Retry successful');
    } catch (error) {
      console.error('Error in retry:', error);
      toast.error('Error during retry. Please try again.');
    } finally {
      setRetryLoading(false);
      setLoading(false); // Hide loading in tabs content
    }
  };

  const getModalTitle = () => {
    switch (currentMode) {
      case LANGUAGE_SELECTION:
        return 'Choose Language';
      case RECORD_CONSULTATION:
        return 'Recording and live transcription';
      case LOADING:
        return 'Processing...';
      case SHOW_SUMMARY:
        return 'Consultation Summary';
      default:
        return 'Ambient Listening';
    }
  };

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile?.id as string);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  useEffect(() => {
    if (doctorLanguage) {
      setLanguage(doctorLanguage);
    } else {
      setLanguage(RecordingLanguage.English);
    }
  }, [doctorLanguage]);

  // Reset form when response data changes (for initial end record)
  useEffect(() => {
    if (response?.summary && currentMode === SHOW_SUMMARY && !retryLoading) {
      // Get current form values before reset
      const currentValues = form.getValues();

      // If we have existing form values, merge them with the new response
      if (Object.keys(currentValues).length > 0) {
        const mergedValues = {
          ...response.summary,
          ...currentValues, // Preserve any user edits
        };
        form.reset(mergedValues);
      } else {
        form.reset(response.summary);
      }
    }
  }, [response, form, currentMode, retryLoading]);

  useEffect(() => {
    if (
      currentMode === LANGUAGE_SELECTION &&
      !engine &&
      !isEngineInitializing
    ) {
      initializeSpeechEngine(language).catch((error) => {
        console.error('Error initializing speech engine on modal open:', error);
      });
    }
  }, [currentMode, language, engine, isEngineInitializing]);

  return (
    <>
      <button
        className={`text-sm flex items-center justify-center gap-1.5 py-1.5 w-full rounded-md ${
          disabled
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-[#4B6BFB] text-white hover:bg-[#3a56d4]'
        }`}
        onClick={!disabled ? startFlow : undefined}
        disabled={disabled}
      >
        Ambient Listening
      </button>

      <AppModal
        open={isModalOpen}
        onClose={handleClose}
        title={
          currentMode === LANGUAGE_SELECTION ||
          currentMode === RECORD_CONSULTATION
            ? modalTitle || getModalTitle()
            : ''
        }
        disableBackdropClick={
          currentMode === RECORD_CONSULTATION ||
          currentMode === LOADING ||
          currentMode === SHOW_SUMMARY
        }
        classes={{
          root:
            currentMode === LANGUAGE_SELECTION
              ? ' max-h-[95vh]  overflow-auto'
              : 'max-h-[95vh] min-h-[65vh] overflow-auto min-w-[60vw]',
          body: '!p-0 flex flex-col flex-1 min-h-0',
        }}
      >
        <section className="flex flex-col flex-1 min-h-0 w-full">
          {(currentMode === LANGUAGE_SELECTION ||
            currentMode === RECORD_CONSULTATION) && (
            <RecordConsultation
              engine={engine}
              onRecordEnd={handleTranscript}
              handleChooseLanguage={handleChooseLanguage}
              selectedLanguage={language}
              currentMode={currentMode}
              setCurrentMode={setCurrentMode}
              isLanguageSelection={currentMode === LANGUAGE_SELECTION}
            />
          )}

          {currentMode === LOADING && (
            <div className="flex flex-1 min-h-[65vh] items-center justify-center w-full">
              <Loading />
            </div>
          )}

          {currentMode === SHOW_SUMMARY && (
            <>
              <div className="flex flex-col h-full max-h-[80vh] relative pb-16">
                <Tabs
                  defaultValue="conversation"
                  className="w-full flex flex-col flex-1 min-h-0"
                >
                  <div className="bg-white sticky top-0 z-10 py-5 pl-6.5 pr-7.5 text-xl font-semibold">
                    <TabsList className="flex w-full justify-between">
                      <TabsTrigger
                        className="flex-1 text-center"
                        value="summary"
                      >
                        {`${summaryFormTitle} Summary (Generated)`}
                      </TabsTrigger>
                      <TabsTrigger
                        className="flex-1 text-center"
                        value="conversation"
                      >
                        Transcription
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <div className="overflow-y-auto flex-1">
                    <TabsContent
                      className="pb-5 pl-6 pr-7.5 min-h-0"
                      value="summary"
                      forceMount
                    >
                      {ambientLoading ? (
                        <div className="flex items-center justify-center h-40">
                          <Loading />
                        </div>
                      ) : customSummaryForm ? (
                        <>{customSummaryForm}</>
                      ) : (
                        <SummaryForm data={response} form={form} editable />
                      )}
                    </TabsContent>

                    <TabsContent className="min-h-0" value="conversation">
                      <div className="pb-5 pl-6 pr-7.5">
                        {ambientLoading ? (
                          <div className="flex items-center justify-center h-40">
                            <Loading />
                          </div>
                        ) : response ? (
                          <Transcript
                            conversation={response?.conversation}
                            duration={response?.summary?.recordingDuration}
                            patientName={patient?.name || 'Patient'}
                            doctorName={
                              doctorProfile?.general?.fullName ||
                              userData.name ||
                              'Doctor'
                            }
                          />
                        ) : (
                          <div className="flex items-center justify-center text-gray-500 h-40">
                            Transcription not generated
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>

                <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-t-[#C2CDD6] z-20 shadow-md">
                  <div className="flex w-full px-6.5 py-3 justify-end gap-4">
                    <PrimaryButton
                      variant="outlined"
                      className="text-[16px] font-normal px-4 h-9 rounded-xl border-black text-black"
                      onClick={handleClose}
                    >
                      Close
                      <BiX />
                    </PrimaryButton>

                    <PrimaryButton
                      variant="contained"
                      className="bg-[#012436] text-white text-[16px] font-normal px-10 h-9 rounded-xl ml-auto"
                      onClick={handleRetry}
                      disabled={retryLoading || isLoading}
                      isLoading={retryLoading}
                    >
                      Retry
                    </PrimaryButton>

                    <PrimaryButton
                      variant="contained"
                      className="bg-[#012436] text-white text-[16px] font-normal px-4 h-9 rounded-xl "
                      onClick={handleSave}
                      disabled={isLoading}
                      isLoading={isLoading}
                    >
                      Save Record
                      <BiSave />
                    </PrimaryButton>
                  </div>
                </div>
              </div>
            </>
          )}
        </section>
      </AppModal>
    </>
  );
};

export default AddRecord;
