import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { foodIntakePatternStore } from '@/store/emr/lifestyle/nutrition/practice/food-intake-pattern-store';

import { detectAmbientInObject } from '@/utils/ambient-detection';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import FoodIntakePatternForm from './FoodIntakePatternForm';

const FoodIntakePatternModal: React.FC<{
  patientData?: QuestionnaireResponse | null;
  mode: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  onSaveRef?: MutableRefObject<(() => void) | null>;
  initialValues?: any;
}> = ({
  patientData,
  mode,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = foodIntakePatternStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();
  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit } = methods;

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        if (data?.id) {
          const updateData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },

            questions:
              data.questions || patientData?.questions || questions.questions,
          };

          if (!updateData.questions || !Array.isArray(updateData.questions)) {
            console.error(
              ' Questions data is missing or invalid:',
              updateData.questions
            );
            throw new Error('Questions data is required for update');
          }

          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };

          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error(' Error submitting food intake pattern:', error);
      }
    },
    [
      setModalOpen,
      profile?.id,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.general?.department,
      updateLifestyleData,
      createLifestyleData,
      patientData?.questions,
      questions.questions,
      onAfterSubmit,
      initialValues, // Add initialValues to dependencies
    ]
  );

  const handleSaveClick = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    } else {
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSaveClick]);

  const formFields = useMemo(() => {
    const dedupeById = (groups?: any[]) => {
      if (!groups || !groups.length) return [];

      const seen = new Set<string>();
      const result: any[] = [];

      for (const group of groups) {
        const key = group?.id;
        if (!key) {
          result.push(group);
          continue;
        }

        if (!seen.has(key)) {
          seen.add(key);
          result.push(group);
        }
      }

      return result;
    };

    if (patientData?.questions && patientData.questions.length > 0) {
      return dedupeById(patientData.questions);
    }

    if (initialValues?.questions && initialValues.questions.length > 0) {
      return dedupeById(initialValues.questions);
    }

    if (questions?.questions?.length) return dedupeById(questions.questions);

    return [];
  }, [patientData, initialValues, questions]);

  // Reset form when patientData or questions change
  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (initialValues) {
      methods.reset(initialValues);
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, initialValues, questions, methods]);

  // Load lifestyle questions on mount
  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  const modalIsAmbient = detectAmbientInObject(initialValues || patientData);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <FoodIntakePatternForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          isAmbientForm={modalIsAmbient}
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(FoodIntakePatternModal);
