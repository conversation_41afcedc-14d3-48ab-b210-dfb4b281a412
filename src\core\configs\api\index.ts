const API_CONFIG = {
  APP_BASE_URL: process.env.NEXT_PUBLIC_URL || 'http://localhost:3001',
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
  API_URL: process.env.NEXT_PUBLIC_API_URL,
  SUBSCRIPTION_KEY: process.env.NEXT_PUBLIC_SUBSCRIPTION_KEY,
  NODE_ENV: process.env.NEXT_PUBLIC_NODE_ENV || 'production',
  CLIENT_ID: process.env.NEXT_PUBLIC_CLIENT_ID || '',
  TENANT_NAME: process.env.NEXT_PUBLIC_TENANT_NAME,
  SIGNIN_POLICY: process.env.NEXT_PUBLIC_SIGNIN_POLICY,
  SPEECH_ENGINE_SUBSCRIPTION_KEY:
    process.env.NEXT_PUBLIC_SPEECH_ENGINE_SUBSCRIPTION_KEY,
  <PERSON>EECH_REGION: process.env.NEXT_PUBLIC_SPEECH_ENGINE_REGION || 'eastus',
  DOC_ASSIST_ENDPOINT: process.env.NEXT_PUBLIC_DOC_ASSIST_ENDPOINT,
};

export default API_CONFIG;
