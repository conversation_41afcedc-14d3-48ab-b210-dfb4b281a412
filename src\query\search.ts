import axios from 'axios';

import API_CONFIG from '@/core/configs/api';

const { DOC_ASSIST_ENDPOINT } = API_CONFIG;

type SearchKnowledgeApiRes = {
  results: string;
};

export async function searchKnowledge(query: string) {
  const { data } = await axios.post<SearchKnowledgeApiRes>(
    `${DOC_ASSIST_ENDPOINT}/document/search/`,
    query,
    {
      headers: {
        'Content-Type': 'text/plain',
      },
    }
  );

  return data.results;
}
