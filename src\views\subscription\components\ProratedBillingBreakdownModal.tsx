import React from 'react';

import AppModal from '@/core/components/app-modal';

interface ProratedBillingBreakdownModalProps {
  open: boolean;
  onClose: () => void;
  proratedInfo: {
    totalDays: number;
    remainingDays: number;
    usedDays: number;
    paidAmount: number;
    dailyRate: number;
    usedAmount: number;
    remainingAmount: number;
    upgradePlanPrice: number;
    proratedPremiumAmount: number;
    currentPlanName: string;
    upgradePlanName: string;
  };
}

const ProratedBillingBreakdownModal: React.FC<
  ProratedBillingBreakdownModalProps
> = ({ open, onClose, proratedInfo }) => {
  return (
    <AppModal
      open={open}
      onClose={onClose}
      title="Price Breakup"
      classes={{
        root: 'w-[45vw] max-h-[90vh] px-1',
        body: 'p-6',
      }}
    >
      <div className="flex flex-col h-[55vh] relative">
        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto px-5">
          <div className="space-y-6">
            <div className="flex justify-between">
              <span className="text-[14px] font-normal text-black">
                Current Plan Amount ({proratedInfo.currentPlanName})
              </span>
              <span className="text-[14px] font-normal text-black">
                ₹{proratedInfo.paidAmount.toFixed(2)}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-[14px] font-normal text-black">
                Amount Used ({proratedInfo.usedDays} days)
              </span>
              <span className="text-[14px] font-normal text-black">
                ₹{proratedInfo.usedAmount.toFixed(2)}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-[14px] font-normal text-black">
                Remaining Credit ({proratedInfo.remainingDays} days)
              </span>
              <span className="text-[14px] font-normal text-black">
                ₹{proratedInfo.remainingAmount.toFixed(2)}
              </span>
            </div>

            <div className="flex justify-between pt-2">
              <span className="text-[14px] font-medium text-black">
                New Plan Amount ({proratedInfo.upgradePlanName})
              </span>
              <span className="text-[14px] font-medium text-black">
                ₹{proratedInfo.upgradePlanPrice.toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Fixed Total Amount at bottom */}
        <div className="absolute bottom-0 left-3 right-3 bg-white border-t pt-4">
          <div className="flex justify-between">
            <span className="text-[16px] font-bold text-black">
              Additional Amount to be Paid
            </span>
            <span className="text-[16px] font-bold text-black">
              ₹{proratedInfo.proratedPremiumAmount.toFixed(2)}
            </span>
          </div>
        </div>
      </div>
    </AppModal>
  );
};

export default ProratedBillingBreakdownModal;
