import React from 'react';

import { UseFormReturn, useWatch } from 'react-hook-form';

import EditableText from '@/lib/common/editable_text';

import { SummarizeConversationRes } from '@/query/speech';

import {
  historyFields,
  lifestyleHealthHistoryFields,
  HistoryField,
} from '@/utils/constants/consultation';
import { isEmptyHtmlContent } from '@/utils/empty-html-detection';

type HistoryTabProps = {
  editable: boolean;
  data: any;
  form: UseFormReturn<SummarizeConversationRes['summary']>;
  isAmbientForm?: boolean;
  expanded?: boolean;
  handleChangeHistoryField: (field: HistoryField, newValue?: string) => void;
  handleChangeLifestyleHistoryField: (
    field: HistoryField,
    newValue?: string
  ) => void;
};

const HistoryTab: React.FC<HistoryTabProps> = ({
  editable,
  data,
  form,
  isAmbientForm,
  expanded = false,
  handleChangeHistoryField,
  handleChangeLifestyleHistoryField,
}) => {
  // Watch all form values for changes
  const formValues =
    useWatch({
      control: form.control,
    }) || {};

  const isFieldEmpty = (value: any): boolean => {
    if (value === null || value === undefined || value === '') return true;
    if (typeof value === 'string') {
      // First check for empty HTML content patterns
      if (isEmptyHtmlContent(value)) {
        return true;
      }

      const trimmedValue = value.trim();
      if (trimmedValue === '') return true;
      const fallbackPhrases = [
        'no information found',
        'no information available',
        'No specific details provided',
        'no information',
      ];
      if (
        fallbackPhrases.some((phrase) =>
          trimmedValue.toLowerCase().includes(phrase.toLowerCase())
        )
      )
        return true;

      if (/^0+(?:\.0+)?$/.test(trimmedValue)) return true;
      return false;
    }
    if (typeof value === 'number') return value === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  };

  const currentMedicationValue =
    (formValues as any)?.currentMedicationHistory ??
    data?.summary?.currentMedicationHistory;
  const currentMedicationEmpty = isFieldEmpty(currentMedicationValue);
  const getDefaultValue = (key: any): string => {
    const value = data?.summary?.[key as keyof typeof data.summary];
    return typeof value === 'string' ? value : '';
  };

  // When not expanded or not editable, use data directly instead of form values
  // This prevents all timeline items from showing the same values
  const getFieldValue = (key: any): string => {
    if (!expanded || !editable) {
      // Use data directly when not expanded/editable
      return getDefaultValue(key);
    }
    // When expanded and editable, use form values
    return (formValues as any)?.[key] ?? getDefaultValue(key);
  };

  const contentClass =
    'min-h-[36px] pt-3 pb-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm';
  const sectionClass = 'flex flex-col gap-2';
  const fieldClass = 'flex flex-col gap-1';

  return (
    <div className="flex flex-col gap-2">
      <section>
        {[historyFields[0], historyFields[1]].map((field) => (
          <EditableText
            key={field.key}
            label={field.label}
            placeholder={field.placeholder}
            defaultValue={getDefaultValue(field.key)}
            value={getFieldValue(field.key)}
            editable={editable}
            bg={'white'}
            emptyPlaceholder={'Nil'}
            labelClassName="font-medium text-sm text-[#001926] mb-1"
            contentClassName="min-h-[36px] pt-3 pb-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
            isAmbientForm={isAmbientForm}
            onChange={(newValue) =>
              handleChangeHistoryField(field.key as HistoryField, newValue)
            }
          />
        ))}
        <div className="grid grid-cols-2 gap-2">
          {historyFields.slice(2).map((field) => (
            <EditableText
              key={field.key}
              label={field.label}
              placeholder={field.placeholder}
              defaultValue={getDefaultValue(field.key)}
              value={getFieldValue(field.key)}
              editable={editable}
              bg={'white'}
              emptyPlaceholder={'Nil'}
              className={fieldClass}
              contentClassName={contentClass}
              isAmbientForm={isAmbientForm}
              onChange={(newValue) =>
                handleChangeHistoryField(field.key as HistoryField, newValue)
              }
            />
          ))}
        </div>
      </section>
      <section className={sectionClass}>
        <h3 className="font-medium text-base text-[#001926]">
          Lifestyle Health History
        </h3>
        <div className="grid grid-cols-2 gap-2">
          {lifestyleHealthHistoryFields.map((field) => (
            <EditableText
              key={field.key}
              label={field.label}
              labelSize="Small"
              placeholder={field.placeholder}
              defaultValue={getDefaultValue(field.key)}
              value={getFieldValue(field.key)}
              editable={editable}
              bg={'white'}
              emptyPlaceholder={'Nil'}
              className={fieldClass}
              contentClassName={contentClass}
              isAmbientForm={isAmbientForm}
              onChange={(newValue) =>
                handleChangeLifestyleHistoryField(
                  field.key as HistoryField,
                  newValue
                )
              }
            />
          ))}
        </div>
      </section>
      <section className={sectionClass}>
        <h3
          className="font-medium text-base text-[#001926]"
          style={{
            color:
              isAmbientForm && currentMedicationEmpty ? '#FF742A' : 'inherit',
          }}
        >
          Current Medical History
        </h3>
        <EditableText
          label=""
          placeholder="Enter Current Medical History"
          defaultValue={data?.summary?.currentMedicationHistory}
          value={
            expanded && editable
              ? ((formValues as any)?.currentMedicationHistory ??
                data?.summary?.currentMedicationHistory)
              : data?.summary?.currentMedicationHistory
          }
          editable={editable}
          bg={'white'}
          emptyPlaceholder={'Nil'}
          contentClassName="min-h-[36px] pt-3 pb-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
          isAmbientForm={isAmbientForm}
          onChange={(newValue) =>
            handleChangeHistoryField('currentMedicationHistory', newValue)
          }
        />
      </section>
    </div>
  );
};

export default HistoryTab;
