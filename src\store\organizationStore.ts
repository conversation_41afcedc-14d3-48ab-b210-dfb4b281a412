import { create } from 'zustand';

import { Organization, organizationQueries } from '@/query/organization';

// Organization types are imported from organization.queries.ts

interface OrganizationState {
  organization: Organization | null;
  isLoading: boolean;
  error: string | null;
  fetchOrganization: (organizationId: string) => Promise<void>;
  clearOrganization: () => void;
}

export const useOrganizationStore = create<OrganizationState>((set) => ({
  organization: null,
  isLoading: false,
  error: null,

  fetchOrganization: async (organizationId: string) => {
    if (!organizationId) {
      console.error('No organization ID provided');
      set({ error: 'No organization ID provided' });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const response =
        await organizationQueries.getOrganization(organizationId);

      // The actual organization data is the response itself, not nested under data
      if (response) {
        set({ organization: response, isLoading: false });
      } else {
        console.error('Empty response received');
        throw new Error('No organization data received');
      }
    } catch (err) {
      console.error('Error in fetchOrganization:', err);
      set({
        error: 'Failed to load organization details',
        isLoading: false,
        organization: null,
      });
    }
  },

  clearOrganization: () => set({ organization: null }),
}));
