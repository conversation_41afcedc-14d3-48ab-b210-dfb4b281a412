import dayjs, { Dayjs } from 'dayjs';
import { toast } from 'sonner';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { modal } from '@/hooks/useModal';

import { useUserStore } from '@/store/userStore';

import {
  createNewPrescription,
  getPrescriptionById,
  getPrescriptionHistory,
  GetPrescriptionHistoryParams,
  PrescriptionPayload,
  searchMedicinesAPI,
  searchPrescriptionHistory,
  updatePrescription,
} from '@/query/emr/prescription';

import { filterBy, FilterByType } from '@/utils/filter-by-util';
import { searchMedicines } from '@/utils/prescription';

import {
  PrescriptionHistory,
  PrescriptionHistoryViewType,
  prescriptionHistoryView,
  PrescriptionItem,
  PrescriptionModeType,
  prescriptionMode,
  prescriptionTabs,
  PrescriptionTabType,
  defaultPrescriptionRow,
} from '@/types/emr/prescription';

export interface PrescriptionState {
  medicines: PrescriptionItem[];
  searchResults: PrescriptionItem[];
  selectedMedicine: PrescriptionItem | null;
  selectedMedicines: PrescriptionItem[];
  prescriptionHistory: PrescriptionHistory[];
  prescriptionHistoryDetail: PrescriptionHistory | null;
  isLoading: boolean;
  historyView: PrescriptionHistoryViewType;
  selectedHistory: PrescriptionHistory | null;
  historyFilter: FilterByType;
  prescriptionMode: PrescriptionModeType;
  activeTab: PrescriptionTabType;
  createdPrescription: PrescriptionHistory | null;
  historySearchQuery: string;
  customStartDate: Dayjs;
  customEndDate: Dayjs;

  showPaymentConfirmationModal: boolean;
  showPaymentSuccessModal: boolean;
  showPaymentFailureModal: boolean;
  isProcessingPayment: boolean;
  paymentErrorMessage: string;

  searchMedicines: (_searchTerm: string) => Promise<PrescriptionItem[]>;
  getMedicineById: (_id: string) => Promise<PrescriptionItem | undefined>;
  selectMedicine: (_medicine: PrescriptionItem | null) => void;
  addSelectedMedicine: (_medicine: PrescriptionItem) => void;
  removeSelectedMedicine: (_id: string) => void;
  clearSelectedMedicines: () => void;
  clearAllMedicines: () => void;
  clearPackageMedicines: () => void;
  setActiveTab: (
    _tab: PrescriptionTabType,
    _medicines?: PrescriptionItem[]
  ) => void;
  createPrescription: (
    _data: PrescriptionPayload,
    _showSuccessMessage?: boolean
  ) => Promise<PrescriptionHistory | null>;
  getPrescriptionHistory: (
    _params: GetPrescriptionHistoryParams
  ) => Promise<PrescriptionHistory[]>;
  getPrescriptionById: (_id: string) => Promise<PrescriptionHistory | null>;
  searchPrescriptionHistory: (
    _searchTerm: string,
    _patientId: string
  ) => Promise<PrescriptionHistory[]>;
  updatePrescription: (
    _id: string,
    _data: PrescriptionPayload
  ) => Promise<PrescriptionHistory | null>;
  setSelectedMedicines: (_medicines: PrescriptionItem[]) => void;
  refillPrescription: () => void;
  setHistoryView: (_historyView: PrescriptionHistoryViewType) => void;
  onExpand: (_history: PrescriptionHistory) => void;
  onBack: () => void;
  setHistoryFilter: (_historyFilter: FilterByType) => void;
  setCustomDateRange: (_startDate: Dayjs, _endDate: Dayjs) => void;
  setPrescriptionMode: (_mode: PrescriptionModeType) => void;
  switchActiveTab: (_tab: PrescriptionTabType) => void;
  editPrescription: () => void;
  searchHistoryExpandedSearch: (_search: string) => void;
  resetHistoryExpandedSearch: () => void;
  setHistorySearchQuery: (_query: string) => void;

  setShowPaymentConfirmationModal: (_show: boolean) => void;
  setShowPaymentSuccessModal: (_show: boolean) => void;
  setShowPaymentFailureModal: (_show: boolean) => void;
  setPaymentErrorMessage: (_message: string) => void;
  setIsProcessingPayment: (_processing: boolean) => void;
}

const { NEW_PRESCRIPTION } = prescriptionTabs;

const { VIEW, NEW, REFILL, EDIT } = prescriptionMode;
const { LIST, DETAILS } = prescriptionHistoryView;
const { ALL } = filterBy;

export const usePrescriptionStore = create<PrescriptionState>()(
  persist(
    (set, get) => ({
      medicines: [],
      searchResults: [],
      selectedMedicine: null,
      selectedMedicines: [],
      prescriptionHistory: [],
      isLoading: false,
      activeTab: NEW_PRESCRIPTION,
      createdPrescription: null,
      prescriptionHistoryDetail: null,
      historyView: LIST,
      selectedHistory: null,
      historyFilter: ALL,
      prescriptionMode: NEW,
      historySearchQuery: '',
      customStartDate: dayjs(),
      customEndDate: dayjs(),

      showPaymentConfirmationModal: false,
      showPaymentSuccessModal: false,
      showPaymentFailureModal: false,
      isProcessingPayment: false,
      paymentErrorMessage: '',

      createPrescription: async (
        data: PrescriptionPayload,
        showSuccessMessage: boolean = true
      ) => {
        set({ isLoading: true });

        try {
          const response = await createNewPrescription(data);
          const newPrescription: PrescriptionHistory =
            (response.data as any)[0]?.data || null;

          set({
            createdPrescription: newPrescription,
            isLoading: false,
          });

          if (showSuccessMessage) {
            modal.success('Prescription created successfully!');
          }

          return newPrescription;
        } catch (error) {
          console.error('Failed to create prescription:', error);
          set({ isLoading: false });
          toast.error('Failed to create prescription.');

          return null;
        }
      },

      updatePrescription: async (id: string, data: PrescriptionPayload) => {
        set({ isLoading: true });

        try {
          const response = await updatePrescription(id, data);
          const updatedPrescription: PrescriptionHistory =
            (response.data as any)[0]?.data || null;

          set({
            createdPrescription: updatedPrescription,
            isLoading: false,
          });

          return updatedPrescription;
        } catch (error) {
          console.error('Failed to update prescription:', error);
          set({ isLoading: false });
          toast.error('Failed to update prescription.');
          return null;
        }
      },

      searchMedicines: async (searchTerm: string) => {
        set({ isLoading: true });

        try {
          const organizationId = useUserStore.getState().data.organizationId;
          const response = await searchMedicinesAPI(searchTerm, organizationId);
          const results: PrescriptionItem[] = response.data.items || [];

          set({ searchResults: results, isLoading: false });
          return results;
        } catch (error) {
          console.error('Error fetching medicines:', error);
          set({ searchResults: [], isLoading: false });
          return [];
        }
      },

      getPrescriptionHistory: async (params) => {
        set({ isLoading: true });

        try {
          const response = await getPrescriptionHistory(params);
          const history: PrescriptionHistory[] = response.data || [];

          history.sort((a, b) => {
            const dateA = new Date(a.updated_on);
            const dateB = new Date(b.updated_on);
            return dateB.getTime() - dateA.getTime();
          });

          set({ prescriptionHistory: history, isLoading: false });
          return history;
        } catch (error) {
          console.error('Error fetching prescription history:', error);
          set({ prescriptionHistory: [], isLoading: false });
          return [];
        }
      },

      getPrescriptionById: async (id: string) => {
        set({ isLoading: true });

        try {
          const response = await getPrescriptionById(id);
          const history: PrescriptionHistory | null = response?.data || null;

          set({ prescriptionHistoryDetail: history, isLoading: false });
          return history;
        } catch (error) {
          console.error('Error fetching prescription by ID:', error);
          set({ prescriptionHistoryDetail: null, isLoading: false });
          return null;
        }
      },

      searchPrescriptionHistory: async (searchTerm, patientId) => {
        set({ isLoading: true });
        try {
          const response = await searchPrescriptionHistory(
            searchTerm,
            patientId
          );
          set({ isLoading: false, prescriptionHistory: response });
          return response;
        } catch (error) {
          console.error('Error fetching prescription history:', error);
          set({ isLoading: false, prescriptionHistory: [] });
          return [];
        }
      },

      getMedicineById: async (id: string) => {
        set({ isLoading: true });
        await new Promise((resolve) => setTimeout(resolve, 200));

        const medicine = get().medicines.find((medicine) => medicine.id === id);
        set({ isLoading: false });

        return medicine;
      },

      selectMedicine: (medicine: PrescriptionItem | null) => {
        set((state) => {
          if (medicine) {
            const alreadyExists = state.selectedMedicines.some(
              (m) => m.id === medicine.id
            );

            if (alreadyExists) {
              toast.error('This medicine has already been added!');
              return {
                selectedMedicine: medicine,
              };
            } else {
              const newMedicine: PrescriptionItem = {
                ...medicine,
                quantity: medicine.quantity || '', // Preserve original quantity or empty string
                cost: String(medicine.Cost || medicine.cost || '0'),
              };

              return {
                selectedMedicine: newMedicine,
                selectedMedicines: [...state.selectedMedicines, newMedicine],
              };
            }
          } else {
            return { selectedMedicine: null };
          }
        });
      },

      setActiveTab: (tab) => set({ activeTab: tab }),

      switchActiveTab: (tab: PrescriptionTabType) => {
        set({
          activeTab: tab,
          selectedMedicines: [defaultPrescriptionRow],
          prescriptionMode: NEW,
          selectedHistory: null,
          historyView: LIST,
        });
      },

      addSelectedMedicine: (medicine: PrescriptionItem) => {
        const existing = get().selectedMedicines.find(
          (m) => m.id === medicine.id
        );
        if (!existing) {
          set((state) => ({
            selectedMedicines: [...state.selectedMedicines, medicine],
          }));
        }
      },

      removeSelectedMedicine: (id: string) => {
        set((state) => ({
          selectedMedicines: state.selectedMedicines.filter(
            (medicine) => medicine.id !== id
          ),
        }));
      },

      clearSelectedMedicines: () => {
        set({ selectedMedicines: [] });
      },

      clearAllMedicines: () => {
        set({ selectedMedicines: [] });
      },

      clearPackageMedicines: () => {
        set((state) => ({
          selectedMedicines: state.selectedMedicines.filter(
            (medicine) =>
              !medicine.packageType ||
              (medicine.packageType !== 'department' &&
                medicine.packageType !== 'user')
          ),
        }));
      },

      setSelectedMedicines: (medicines: PrescriptionItem[]) => {
        set({ selectedMedicines: medicines });
      },

      refillPrescription: () => {
        const medicines = get().medicines;
        set({
          selectedMedicines: medicines,
          prescriptionMode: REFILL,
          activeTab: NEW_PRESCRIPTION,
          selectedHistory: { medicines: medicines } as any,
          historyView: LIST,
        });
      },

      editPrescription: () => {
        const medicines = get().medicines;
        const history = { ...get().selectedHistory, medicines };

        set({
          selectedMedicines: medicines,
          prescriptionMode: EDIT,
          activeTab: NEW_PRESCRIPTION,
          selectedHistory: history as PrescriptionHistory,
          historyView: LIST,
        });
      },

      setHistoryView: (historyView) => {
        set({ historyView });
      },

      onExpand: (history) => {
        set({
          selectedHistory: history,
          selectedMedicines: history?.medicines || [],
          historyView: DETAILS,
          prescriptionMode: VIEW,
          medicines: history?.medicines || [],
          historySearchQuery: '',
        });
      },
      onBack: () => {
        set({
          selectedHistory: null,
          historyView: LIST,
          prescriptionMode: VIEW,
          historySearchQuery: '',
        });
      },
      setHistoryFilter: (historyFilter) => {
        set({
          historyFilter,
          customEndDate: dayjs(),
          customStartDate: dayjs(),
        });
      },
      setPrescriptionMode: (mode) => {
        set({ prescriptionMode: mode });
      },

      searchHistoryExpandedSearch: (search) => {
        const currentHistory = get().selectedHistory;
        const filteredMedicines = searchMedicines(get().medicines, search);

        set({
          selectedHistory: {
            ...currentHistory,
            medicines: filteredMedicines,
          } as PrescriptionHistory,
        });
      },

      resetHistoryExpandedSearch: () => {
        const medicines = get().medicines;
        const selectedHistory = get().selectedHistory;
        set({
          selectedHistory: {
            ...selectedHistory,
            medicines: medicines || [],
          } as PrescriptionHistory,
        });
      },

      setHistorySearchQuery: (query) => {
        set({ historySearchQuery: query });
      },
      setCustomDateRange: (startDate, endDate) => {
        set({
          customStartDate: startDate,
          customEndDate: endDate,
        });
      },

      setShowPaymentConfirmationModal: (show) => {
        set({ showPaymentConfirmationModal: show });
      },

      setShowPaymentSuccessModal: (show) => {
        set({ showPaymentSuccessModal: show });
      },

      setShowPaymentFailureModal: (show) => {
        set({ showPaymentFailureModal: show });
      },

      setPaymentErrorMessage: (message) => {
        set({ paymentErrorMessage: message });
      },

      setIsProcessingPayment: (processing) => {
        set({ isProcessingPayment: processing });
      },
    }),
    {
      name: 'prescription-store',
      partialize: ({
        historyFilter,
        activeTab,
        customStartDate,
        customEndDate,
      }) => ({
        activeTab,
        historyFilter,
        customStartDate,
        customEndDate,
      }),
    }
  )
);
