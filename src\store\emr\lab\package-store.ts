import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createTestPackage,
  fetchTestPackages,
  fetchTestsByPackageId,
  updateTestPackage,
  fetchUserPackage,
} from '@/query/emr/lab';

import {
  ModalMode,
  modalModes,
  PackageType,
  packageTypes,
  LabTestItem,
} from '@/types/emr/lab';

import { useUserStore } from '../../userStore';

import { useTestStore } from './reports-store';
export interface TestItem {
  id: string | number;
  testName: string;
  selected: boolean;
  cost?: number | string;
  department?: string;
  status?: string;
  organizationCost?: number;
  price?: number;
}
export interface PackageData {
  id: string | number;
  name: string;
  tests: TestItem[];
  type: PackageType;
}
interface PackageSelectorState {
  isModalOpen: boolean;
  modalMode: ModalMode;
  packages: PackageData[];
  activePackageType: PackageType;
  selectedPackage: PackageData | null;
  newPackageName: string;
  testItems: TestItem[];
  tempSearchTests: Array<{ id?: string | number; name?: string }>;
  isLoading: boolean;
  isCreating: boolean;

  openModal: (_packageType: PackageType) => void;
  closeModal: () => void;
  setModalMode: (_mode: ModalMode) => void;
  setSelectedPackage: (_pkg: PackageData) => void;
  setNewPackageName: (_name: string) => void;
  addMultipleTests: (
    _tests: Array<{ id: string | number; name: string }>
  ) => void;
  removeTest: (_testId: string | number) => void;
  toggleTestSelection: (_testId: string | number) => void;
  createNewPackage: () => void;
  updateExistingPackage: () => void;
  startCreatePackage: (_modalMode: ModalMode) => void;
  addSelectedPackageToTest: () => void;
  getPackagesByType: () => PackageData[];
  fetchPackages: () => Promise<void>;
}

export const usePackageSelectorStore = create<PackageSelectorState>(
  (set, get) => ({
    isModalOpen: false,
    modalMode: modalModes.VIEW,
    packages: [],
    activePackageType: null,
    selectedPackage: null,
    newPackageName: '',
    testItems: [],
    tempSearchTests: [],
    isLoading: false,
    isCreating: false,

    openModal: (packageType) =>
      set({
        isModalOpen: true,
        activePackageType: packageType,
        modalMode: modalModes.VIEW,
        selectedPackage: null,
        testItems: [],
        tempSearchTests: [],
      }),

    closeModal: () => {
      useTestStore.getState().clearPackageTests();
      set({
        isModalOpen: false,
        modalMode: modalModes.VIEW,
        selectedPackage: null,
        testItems: [],
        newPackageName: '',
        tempSearchTests: [],
        activePackageType: null,
      });
    },

    setModalMode: (mode) => set({ modalMode: mode }),

    setSelectedPackage: async (pkg) => {
      set({
        selectedPackage: pkg,
        modalMode: modalModes.DETAIL,
        testItems: [],
        isLoading: true,
      });

      try {
        const organizationId = useUserStore.getState().data?.organizationId;
        const response = await fetchTestsByPackageId(
          pkg.id as string,
          organizationId
        );
        const tests: TestItem[] = response.data.map((test: any) => {
          const orgCost = Number(test.organizationCost) || 0;
          const price = Number(test.price) || 0;
          const mappedTest = {
            id: test.testId,
            testName: test.testName,
            selected: false,
            organizationCost: orgCost,
            price: price,
            cost: String(orgCost > 0 ? orgCost : price),
            department: test.department || '',
            status: test.status || 'pending',
          };

          return mappedTest;
        });
        set({ testItems: tests });
      } catch (error) {
        toast.error('Failed to load tests');
        console.error('Fetch tests error:', error);
      } finally {
        set({ isLoading: false });
      }
    },

    setNewPackageName: (name) => set({ newPackageName: name }),

    addMultipleTests: (
      tests: Array<{
        id: string | number;
        name: string;
        organizationCost?: number;
        price?: number;
      }>
    ) =>
      set((state) => {
        const existingIds = new Set(state.testItems.map((item) => item.id));
        const alreadyExistingTests = tests.filter((test) =>
          existingIds.has(test.id)
        );

        const newTests = tests
          .filter((test) => !existingIds.has(test.id))
          .map((test) => {
            const orgCost = Number(test.organizationCost) || 0;
            const price = Number(test.price) || 0;
            return {
              id: test.id,
              testName: test.name,
              selected: false,
              organizationCost: orgCost,
              price,
              cost: String(orgCost > 0 ? orgCost : price),
            };
          });
        if (state.testItems.length + newTests.length > 30) {
          toast.error(`Maximum limit of 30 tests allowed`);
          return state;
        }

        if (alreadyExistingTests.length > 0 && newTests.length === 0) {
          toast.error(`This test has already been added`);
          return state;
        }

        return {
          testItems: [...state.testItems, ...newTests],
          tempSearchTests: [
            ...state.tempSearchTests,
            ...tests.filter((t) => !existingIds.has(t.id)),
          ],
        };
      }),

    removeTest: (testId) =>
      set((state) => ({
        testItems: state.testItems.filter((test) => test.id !== testId),
        tempSearchTests: state.tempSearchTests.filter(
          (test) => test.id !== testId
        ),
      })),

    toggleTestSelection: (testId) =>
      set((state) => ({
        testItems: state.testItems.map((test) =>
          test.id === testId ? { ...test, selected: !test.selected } : test
        ),
      })),

    createNewPackage: async () => {
      const {
        newPackageName,
        testItems,
        activePackageType,
        fetchPackages,
        packages,
      } = get();
      const id = useUserStore.getState().data.id;

      if (!newPackageName.trim()) {
        toast.error('Package name is required');
        return;
      }

      if (testItems.length === 0) {
        toast.error('At least one test must be added');
        return;
      }

      const isDuplicate = packages.some(
        (pkg) =>
          pkg.name.toLowerCase().trim() ===
            newPackageName.toLowerCase().trim() &&
          pkg.type === activePackageType
      );

      if (isDuplicate) {
        toast.error('A package with this name already exists');
        return;
      }

      set({ isCreating: true });

      try {
        const payload = {
          name: newPackageName,
          type: activePackageType,
          userId: id,
          tests: testItems.map((t) => ({ testId: t.id, testName: t.testName })),
        };

        const response = await createTestPackage(
          payload,
          activePackageType as string
        );
        const newPackage = response?.data;

        await fetchPackages();

        set({
          modalMode: modalModes.DETAIL,
          newPackageName: '',
          selectedPackage: {
            id: newPackage.id as string,
            name: newPackage.name,
            type: activePackageType!,
            tests: testItems,
          },
        });
      } catch (error) {
        toast.error('Failed to create package');
        console.error('Package creation failed:', error);
      } finally {
        set({ isCreating: false });
      }
    },

    fetchPackages: async () => {
      const { activePackageType } = get();

      if (!activePackageType) {
        toast.error('Package type is not selected');
        return;
      }

      set({ isLoading: true });

      try {
        let response;
        if (activePackageType === packageTypes.USER) {
          const userId = useUserStore.getState().data.id;
          response = await fetchUserPackage(userId);
        } else {
          response = await fetchTestPackages(activePackageType);
        }

        const packages: PackageData[] = response.data.map((pkg: any) => ({
          id: pkg.id,
          name: pkg.name,
          type: activePackageType,
        }));

        set({ packages });
      } catch (error) {
        toast.error('Failed to fetch packages');
        console.error('Fetch packages error:', error);
      } finally {
        set({ isLoading: false });
      }
    },
    updateExistingPackage: async () => {
      const {
        selectedPackage,
        testItems,
        fetchPackages,
        newPackageName,
        packages,
      } = get();

      if (!selectedPackage) {
        toast.error('No package selected');
        return;
      }

      if (!newPackageName.trim()) {
        toast.error('Package name is required');
        return;
      }

      if (testItems.length === 0) {
        toast.error('At least one test must be added');
        return;
      }

      const isDuplicate = packages.some(
        (pkg) =>
          pkg.name.toLowerCase().trim() ===
            newPackageName.toLowerCase().trim() &&
          pkg.id !== selectedPackage.id &&
          pkg.type === selectedPackage.type
      );

      if (isDuplicate) {
        toast.error('A package with this name already exists');
        return;
      }

      set({ isCreating: true });

      try {
        const payload = {
          packageId: String(selectedPackage.id),
          updates: {
            name: newPackageName,
            type: selectedPackage.type as string,
            tests: testItems.map((t) => ({
              testName: t.testName,
              testId: t.id,
            })),
          },
        };

        await updateTestPackage(payload);

        toast.success('Package updated successfully');
        await fetchPackages();
        set({ modalMode: modalModes.DETAIL });
      } catch (error) {
        toast.error('Failed to update package');
        console.error('Package update error:', error);
      } finally {
        set({ isCreating: false });
      }
    },

    startCreatePackage: (mode: ModalMode) => {
      const selectedSearchTests = useTestStore.getState().selectedSearchTests;

      const mappedTests: TestItem[] = selectedSearchTests.map((test) => ({
        id: test.id as string,
        testName: test.name as string,
        selected: false,
      }));

      set({
        modalMode: mode,
        selectedPackage: null,
        testItems: mode === modalModes.ADD ? mappedTests : [],
        newPackageName: '',
        tempSearchTests: mode === modalModes.ADD ? selectedSearchTests : [],
      });
    },

    addSelectedPackageToTest: () => {
      const { testItems } = get();
      const selectedTests = testItems.filter((item) => item.selected);

      if (selectedTests.length === 0) {
        toast.error('No tests selected');
        return;
      }

      const currentPageKey = packageTypes.DEFAULT;

      selectedTests.forEach((t) => {
        const testToAdd: LabTestItem = {
          id: String(t.id),
          name: t.testName,
          cost: String(
            t.organizationCost && t.organizationCost > 0
              ? t.organizationCost
              : t.price && t.price > 0
                ? t.price
                : 0
          ),
          department: t.department || '',
          status: t.status || 'pending',
          testId: String(t.id),
        };

        useTestStore
          .getState()
          .setSelectedSearchTest(testToAdd, currentPageKey);
      });

      set({
        isModalOpen: false,
        activePackageType: null,
        testItems: testItems.map((item) => ({ ...item, selected: false })),
      });
    },

    getPackagesByType: () => {
      const { packages, activePackageType } = get();
      return packages.filter((pkg) => pkg.type === activePackageType);
    },
  })
);
