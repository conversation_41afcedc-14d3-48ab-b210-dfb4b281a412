import React, { Fragment, useCallback } from 'react';

import { Controller } from 'react-hook-form';

import { TextareaAutosize, Box, Typography } from '@mui/material';

import { cn } from '@/lib/utils';

import { allowNumbersWithDecimals } from '@/utils/validation';

import PenIcon from '@/assets/svg/PenIcon';

import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';

import AppRadio from '@/core/components/app-radio';
import AppSlider from '@/core/components/app-slider';
import AppTextField from '@/core/components/app-text-field';
import {
  RadioField as IRadioField,
  SliderField as ISliderField,
  ConditionalField as IConditionalField,
  NumberField as INumberField,
  TextField as ITextField,
  SectionField as ISectionField,
} from '@/types/emr/lifestyle/questionnaire';

import { GroupedTableField } from './GroupedTableField';
import { TableField } from './TableField';
import { FieldComponentProps } from './types';

// Helper function to check if a field value is empty or unanswered
const isFieldEmpty = (value: any, field?: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return true;
  }
  if (Array.isArray(value) && value.length === 0) {
    return true;
  }
  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true;
  }

  // Check for "No information found" or similar ambient listening fallback values
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase().trim();
    if (
      lowerValue.includes('no information found') ||
      lowerValue.includes('not found') ||
      lowerValue.includes('no data') ||
      lowerValue.includes('unknown')
    ) {
      return true;
    }

    // If field has options, check if the value is not in the valid options
    if (field?.options && Array.isArray(field.options)) {
      const validOptions = field.options.map((opt: string) =>
        opt.toLowerCase().trim()
      );
      const isValid = validOptions.includes(lowerValue);

      if (!isValid) {
        return true;
      }
    }
  }

  return false;
};

// Helper function to format backend fallback values (like "No information found")
// for display purposes. Any such fallback will be shown as "-" in the UI.
const formatDisplayValue = (value: any): any => {
  if (typeof value !== 'string') return value;

  const lowerValue = value.toLowerCase().trim();
  if (
    lowerValue.includes('no information found') ||
    lowerValue.includes('not found') ||
    lowerValue.includes('no data') ||
    lowerValue.includes('unknown')
  ) {
    return '-';
  }

  return value;
};

// Helper function to get highlight styles for unanswered questions in ambient forms
const getHighlightStyles = (isAmbientForm: boolean, isEmpty: boolean) => {
  if (isAmbientForm && isEmpty) {
    return {
      color: '#FF742A',
    };
  }
  return {};
};

export const RadioField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  fieldIndex = 0,
  variant,
  sectionId,
  isAmbientForm,
}) => {
  const radioField = field as IRadioField;
  const radioOptions = radioField.options.map((option: string) => ({
    value: option,
    label: option,
  }));

  // Extract custom styles from field
  const hasCustomStyles = !!(field as any).customStyles;
  const customStyles = (field as any).customStyles || {};

  const isMainAttitudeQuestion =
    field.id === 'eating_healthy_food' || field.id === 'exercise_preference';

  // Check if this is the specific meal skip question that should be excluded
  const isMealSkipQuestion = field.label
    ?.toLowerCase()
    .includes('which meal do you skip');

  const isKnowledgeQuestion =
    !isMealSkipQuestion &&
    (field.label?.toLowerCase().includes('benefit of exercise') ||
      field.label?.toLowerCase().includes('examples of') ||
      field.label?.toLowerCase().includes('training') ||
      field.label?.toLowerCase().includes('exercise') ||
      field.label?.toLowerCase().includes('walking') ||
      field.label?.toLowerCase().includes('material') ||
      field.label?.toLowerCase().includes('food') ||
      field.label?.toLowerCase().includes('minimum') ||
      field.label?.toLowerCase().includes('which') ||
      field.label?.toLowerCase().includes('what is') ||
      field.label?.toLowerCase().includes('high carbohydrate content') ||
      field.label?.toLowerCase().includes('substitute for ragi') ||
      field.label?.toLowerCase().includes('processed foods') ||
      field.label?.toLowerCase().includes('high protein content') ||
      field.label?.toLowerCase().includes('high unsaturated fat content') ||
      field.label?.toLowerCase().includes('high saturated fat content') ||
      field.label?.toLowerCase().includes('healthy fat') ||
      field.label?.toLowerCase().includes('vitamin & mineral content') ||
      field.label?.toLowerCase().includes('high in fibre content') ||
      field.label?.toLowerCase().includes('high in salt content') ||
      field.label?.toLowerCase().includes('best cooking method') ||
      field.label?.toLowerCase().includes('expensive with marginal benefits') ||
      field.label?.toLowerCase().includes('associated with longevity') ||
      field.label?.toLowerCase().includes('most important meal') ||
      field.label?.toLowerCase().includes('the risk for heart disease') ||
      field.label?.toLowerCase().includes('recommended water intake') ||
      field.label
        ?.toLowerCase()
        .includes('recommended daily servings of fruits & vegetables') ||
      field.label?.toLowerCase().includes('high fibre intake prevents all') ||
      field.label?.toLowerCase().includes('high salt intake increases') ||
      field.label?.toLowerCase().includes('artificial sweeteners') ||
      field.label?.toLowerCase().includes('whole plant foods'));

  if (readonly) {
    // Special handling for modal variant: show both radio options with the selected one marked
    const isExerciseLikeQuestion = field.label
      ?.toLowerCase()
      .includes('do you like to exercise');
    const isEatingHealthyLikeQuestion =
      (field.label?.toLowerCase().includes('do you like eating healthy food') ??
        false) ||
      field.id === 'eating_healthy_food';

    if (
      variant === 'modal' &&
      (isExerciseLikeQuestion || isEatingHealthyLikeQuestion)
    ) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            return (
              <div
                className="border-t border-b border-gray-300"
                style={{ borderColor: '#DAE1E7' }}
              >
                <div className="flex items-center py-3 px-4 bg-white">
                  <span
                    className="font-bold text-sm mr-8"
                    style={{
                      fontSize: '14px',
                      fontWeight: 700,
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {field.label}
                  </span>
                  <div className="flex items-center gap-4">
                    {radioField.options.map((option) => (
                      <label key={option} className="flex items-center gap-2">
                        <input
                          type="radio"
                          name={name}
                          value={option}
                          checked={fieldValue === option}
                          readOnly
                          className="w-4 h-4 border-gray-300 focus:ring-black text-black"
                          style={{
                            accentColor: '#000000',
                            color: '#000000',
                          }}
                        />
                        <span className="text-sm">{option}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            );
          }}
        />
      );
    }

    if (isMainAttitudeQuestion) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            return (
              <div className="flex items-center gap-4">
                <span className="text-base font-medium">{field.label}</span>
                <span
                  className={`text-base ${variant === 'timeline' ? 'font-normal' : 'font-semibold'}`}
                >
                  {formatDisplayValue(fieldValue) || 'No answer provided'}
                </span>
              </div>
            );
          }}
        />
      );
    }

    if (isKnowledgeQuestion) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;
            const isEvenRow = fieldIndex % 2 === 0;
            const rowBgColor = isEvenRow ? 'bg-gray-100' : 'bg-white';

            // Consider empty when value is missing, not in options, or is an ambient fallback
            const isEmpty = isFieldEmpty(fieldValue, field);
            // Highlight in knowledge timeline/read-only if empty
            const highlightColor = isEmpty ? '#FF742A' : undefined;

            return (
              <div
                className={`grid grid-cols-5 gap-0 border border-gray-300 divide-x divide-gray-300 min-h-[60px] items-start ${rowBgColor}`}
              >
                <div className="p-2 flex items-start">
                  <span
                    className="text-sm text-gray-700 leading-relaxed break-words"
                    style={{ color: highlightColor || 'inherit' }}
                  >
                    {field.label}
                  </span>
                </div>

                {[0, 1, 2, 3].map((optionIndex) => {
                  const option = radioOptions[optionIndex];
                  const isChecked =
                    String(fieldValue ?? '')
                      .trim()
                      .toLowerCase() ===
                    String(option?.value ?? '')
                      .trim()
                      .toLowerCase();

                  return (
                    <div
                      key={optionIndex}
                      className=" flex justify-center w-full h-full"
                      style={{ paddingTop: '12px' }}
                    >
                      {option ? (
                        <label className="flex items-start cursor-default w-[140px]">
                          <span
                            className={`text-sm leading-tight break-words ml-2 ${
                              isChecked
                                ? 'font-bold text-black'
                                : 'text-gray-600'
                            }`}
                            style={{
                              color:
                                !isChecked && highlightColor
                                  ? highlightColor
                                  : undefined,
                            }}
                          >
                            {option.label}
                          </span>
                        </label>
                      ) : (
                        <div className="w-4 h-4"></div>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          }}
        />
      );
    }

    // Check if this is a food intake form in read-only mode
    const isFoodIntake = sectionId === 'food_intake';

    if (isFoodIntake) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            const isEmpty = isFieldEmpty(fieldValue, field);
            const highlightStyles = getHighlightStyles(
              isAmbientForm || false,
              isEmpty
            );

            // In ambient/timeline view, when data comes from ambient listening
            // and is effectively "missing", show "-" instead of "No answer provided"
            const displayValue = isEmpty ? '-' : formatDisplayValue(fieldValue);

            return (
              <div className="w-full">
                <div className="flex flex-col">
                  <span
                    className="text-base font-medium"
                    style={{ color: highlightStyles.color || 'inherit' }}
                  >
                    Q. {field.label}
                  </span>
                  <span
                    className="text-base"
                    style={{ color: highlightStyles.color || 'inherit' }}
                  >
                    A:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {displayValue ||
                      (isAmbientForm ? '-' : 'No answer provided')}
                  </span>
                </div>
              </div>
            );
          }}
        />
      );
    }

    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { value } }) => {
          const fieldValue =
            (field as any).value !== undefined ? (field as any).value : value;

          return (
            <div className="flex flex-col">
              <span className="text-base font-medium">Q. {field.label}</span>
              <span className="text-base">
                A:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                {formatDisplayValue(fieldValue) || 'No answer provided'}
              </span>
            </div>
          );
        }}
      />
    );
  }

  const isMainExerciseQuestion = field.label
    ?.toLowerCase()
    .includes('do you like to exercise');

  const defaultFormControlProps = {
    sx: {
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      gap: isMainExerciseQuestion ? 1 : isKnowledgeQuestion ? 0.5 : 2,
      alignItems: 'center',
      minHeight: isKnowledgeQuestion ? '32px' : 'auto',
      ['& .app-input-label']: {
        minWidth: isKnowledgeQuestion ? 250 : 400,
        maxWidth: isKnowledgeQuestion ? 250 : 'none',
        fontSize: isKnowledgeQuestion ? '13px' : '16px',
        lineHeight: isKnowledgeQuestion ? '1.2' : 'normal',
        ...(isMainExerciseQuestion && {
          fontWeight: 'bold',
          marginBottom: '4px',
        }),
        ...(isKnowledgeQuestion && {
          fontWeight: '400',
          marginBottom: '0px',
          paddingRight: '8px',
        }),
      },
      ['& .MuiRadioGroup-root']: {
        gap: isMainExerciseQuestion
          ? 1
          : isKnowledgeQuestion
            ? 0.5
            : hasCustomStyles
              ? customStyles.radioGroupGap
              : 2,

        ...(isKnowledgeQuestion && {
          gap: '4px',
          flexWrap: 'wrap',
        }),
      },
      ['& .MuiFormControlLabel-root']: {
        ...(isKnowledgeQuestion && {
          margin: '0 8px 0 0',
          '& .MuiFormControlLabel-label': {
            fontSize: '12px',
            lineHeight: '1.2',
          },
        }),
      },
    },
  };

  const formControlProps =
    (radioField as any).formControlProps || defaultFormControlProps;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : value;

        const radioComponent = (
          <AppRadio
            name={name}
            value={fieldValue}
            onChange={onChange}
            label={field.label}
            options={radioOptions}
            errors={errors?.[name]}
            required={isMainExerciseQuestion ? false : field.required}
            disabled={readonly}
            formControlProps={formControlProps}
          />
        );

        if (isMainAttitudeQuestion) {
          const isEmpty = isFieldEmpty(fieldValue, field);
          const highlightStyles = getHighlightStyles(
            isAmbientForm || false,
            isEmpty
          );

          if (readonly) {
            return (
              <div className="flex items-center gap-4">
                <span
                  className="text-base font-medium"
                  style={{ color: highlightStyles.color || 'inherit' }}
                >
                  {field.label}
                </span>
                <span
                  className="text-base font-semibold"
                  style={{ color: highlightStyles.color || 'inherit' }}
                >
                  {fieldValue || 'No answer provided'}
                </span>
              </div>
            );
          }
          return (
            <div className="border-t border-b border-gray-300">
              <div className="flex items-center py-3 px-4 bg-white">
                <span
                  className="font-bold text-sm mr-8"
                  style={{
                    fontSize: '14px',
                    fontWeight: 700,
                    whiteSpace: 'nowrap',
                    color: highlightStyles.color || 'inherit',
                  }}
                >
                  {field.label}
                </span>
                <div className="flex items-center gap-4">
                  {radioField.options.map((option) => (
                    <label
                      key={option}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <input
                        type="radio"
                        name={name}
                        value={option}
                        checked={fieldValue === option}
                        onChange={() => {
                          if (!readonly) {
                            onChange(option);
                          }
                        }}
                        readOnly={readonly}
                        className="w-4 h-4 border-gray-300 focus:ring-black text-black"
                        style={{
                          accentColor: highlightStyles.color || '#000000',
                          color: highlightStyles.color || '#000000',
                          opacity: readonly ? 1 : 1,
                        }}
                      />
                      <span
                        className="text-sm"
                        style={{ color: highlightStyles.color || 'inherit' }}
                      >
                        {option}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          );
        }

        if (isMainExerciseQuestion) {
          return (
            <div className=" border-b border-gray-300 py-4">
              {radioComponent}
            </div>
          );
        } else if (isKnowledgeQuestion) {
          const isEvenRow = fieldIndex % 2 === 0;
          const rowBgColor = isEvenRow ? 'bg-sky-100' : 'bg-white';
          const isEmpty = isFieldEmpty(fieldValue, field);
          const highlightStyles = getHighlightStyles(
            isAmbientForm || false,
            isEmpty
          );

          return (
            <div
              className={`grid grid-cols-5 gap-0 border border-gray-300 divide-x divide-gray-300  min-h-[60px] items-start ${rowBgColor}`}
            >
              <div className="p-2 flex items-start ">
                <span
                  className="text-sm text-gray-900 leading-relaxed break-words"
                  style={{ color: highlightStyles.color || 'inherit' }}
                >
                  {field.label}
                </span>
              </div>

              {[0, 1, 2, 3].map((optionIndex) => {
                const option = radioOptions[optionIndex];
                return (
                  <div
                    key={optionIndex}
                    className=" flex justify-center w-full h-full  "
                    style={{ paddingTop: '12px' }}
                  >
                    {option ? (
                      <label className="flex items-start cursor-pointer  px-2 w-[150px]">
                        <input
                          type="radio"
                          name={name}
                          value={option.value}
                          checked={fieldValue === option.value}
                          onChange={() => onChange(option.value)}
                          className="w-4 h-4 accent-black border-gray-300 focus:ring-black flex-shrink-0"
                          hidden={readonly}
                          style={{
                            marginTop: '0px',
                            accentColor: highlightStyles.color || '#000000',
                            opacity: readonly ? 1 : 1,
                          }}
                        />
                        <span
                          className="ml-2 text-sm text-gray-900 leading-tight break-words"
                          style={{ color: highlightStyles.color || 'inherit' }}
                        >
                          {option.label}
                        </span>
                      </label>
                    ) : (
                      <div className="w-4 h-4"></div>
                    )}
                  </div>
                );
              })}
            </div>
          );
        } else {
          const isEmpty = isFieldEmpty(fieldValue, field);
          const highlightStyles = getHighlightStyles(
            isAmbientForm || false,
            isEmpty
          );

          // Apply highlighting to the radio component if it's an ambient form with empty values
          const styledRadioComponent =
            isAmbientForm && isEmpty ? (
              <div style={highlightStyles}>{radioComponent}</div>
            ) : (
              radioComponent
            );

          return customStyles.wrapperClassName ? (
            <div className={customStyles.wrapperClassName}>
              {styledRadioComponent}
            </div>
          ) : (
            styledRadioComponent
          );
        }
      }}
    />
  );
};

export const SliderField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  sectionId,
  isAmbientForm,
  watch,
  setValue,
}) => {
  const sliderField = field as ISliderField;

  // Check if field has custom styles defined
  const customStyles = (field as any).customStyles || {};

  // Check if this is a physical activity attitude section slider (importance/confidence)
  // We only suppress helperText for physical activity attitude sliders since they have manual labels
  const isPhysicalActivityAttitudeSlider =
    (name.includes('importance') ||
      name.includes('confidence') ||
      field.label?.toLowerCase().includes('important') ||
      field.label?.toLowerCase().includes('confident')) &&
    name.toLowerCase().includes('physical');

  // Check if this slider is inside a section (nutrition/physical attitude)
  const isInSection =
    name.includes('.fields.') &&
    (name.toLowerCase().includes('nutrition') ||
      name.toLowerCase().includes('physical') ||
      name.toLowerCase().includes('importance') ||
      name.toLowerCase().includes('confidence'));

  const minValue = 0;

  const directValue = (field as any).value;
  const formValue = watch ? watch(name) : undefined;

  // Initialize form value if direct value exists and form value is empty
  React.useEffect(() => {
    if (
      !readonly &&
      setValue &&
      directValue !== undefined &&
      directValue !== null &&
      directValue !== '' &&
      (formValue === undefined || formValue === null || formValue === '')
    ) {
      const numValue =
        typeof directValue === 'number' ? directValue : Number(directValue);
      if (!isNaN(numValue)) {
        const lowerDirectValue = String(directValue).toLowerCase().trim();
        if (
          !lowerDirectValue.includes('no information found') &&
          !lowerDirectValue.includes('not found') &&
          !lowerDirectValue.includes('no data') &&
          !lowerDirectValue.includes('unknown')
        ) {
          setValue(name, numValue);
        }
      }
    }
  }, [directValue, formValue, name, setValue, readonly]);

  // Helper function to resolve field value from multiple sources
  const resolveFieldValue = (formVal: any, directVal: any): any => {
    // Priority 1: Form value (from nested structure or form state)
    if (formVal !== undefined && formVal !== null && formVal !== '') {
      // Check if it's a valid numeric value (not "No information found" etc.)
      if (typeof formVal === 'number') {
        return formVal;
      }
      if (typeof formVal === 'string') {
        const lowerValue = formVal.toLowerCase().trim();
        if (
          !lowerValue.includes('no information found') &&
          !lowerValue.includes('not found') &&
          !lowerValue.includes('no data') &&
          !lowerValue.includes('unknown') &&
          lowerValue !== ''
        ) {
          // Try to parse as number
          const numValue = Number(formVal);
          if (!isNaN(numValue)) {
            return numValue;
          }
        }
      }
    }

    // Priority 2: Direct field value
    if (directVal !== undefined && directVal !== null && directVal !== '') {
      if (typeof directVal === 'number') {
        return directVal;
      }
      if (typeof directVal === 'string') {
        const lowerValue = directVal.toLowerCase().trim();
        if (
          !lowerValue.includes('no information found') &&
          !lowerValue.includes('not found') &&
          !lowerValue.includes('no data') &&
          !lowerValue.includes('unknown') &&
          lowerValue !== ''
        ) {
          // Try to parse as number
          const numValue = Number(directVal);
          if (!isNaN(numValue)) {
            return numValue;
          }
        }
      }
    }

    return formVal ?? directVal ?? undefined;
  };

  const defaultValue = resolveFieldValue(formValue, directValue);

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field: { onChange, value } }) => {
        // Resolve value: check form value first, then direct field value
        const currentFormValue = value;
        const resolvedValue = resolveFieldValue(currentFormValue, directValue);

        const fieldValue = readonly ? resolvedValue : resolvedValue;
        const isEmpty = isFieldEmpty(fieldValue, field);
        const highlightStyles = getHighlightStyles(
          isAmbientForm || false,
          isEmpty
        );

        // For sliders in sections, if empty, highlight label only, not the slider
        // Slider still needs a value for rendering, but we won't highlight the slider itself
        const sliderValue = fieldValue ?? minValue;

        return (
          <AppSlider
            value={sliderValue}
            onChange={onChange}
            label={field.label}
            min={minValue}
            max={sliderField.max}
            step={sliderField.step}
            helperText={
              isPhysicalActivityAttitudeSlider ? undefined : field.description
            }
            errors={errors?.[name]}
            required={field.required}
            showIndicator
            disabled={readonly}
            customStyles={{
              ...customStyles,
              // Only highlight slider color if not in section or not empty
              ...(isAmbientForm &&
                isEmpty &&
                !isInSection && {
                  labelColor: highlightStyles.color,
                  sliderColor: highlightStyles.color,
                }),
              // Always highlight label if empty in section
              ...(isAmbientForm &&
                isEmpty &&
                isInSection && {
                  labelColor: highlightStyles.color,
                }),
            }}
            labelVariant={
              sectionId === 'food_intake' && readonly ? 'vertical' : undefined
            }
            labelClassName={cn(
              customStyles.labelMinWidth
                ? `min-w-[${customStyles.labelMinWidth}]`
                : undefined,
              isAmbientForm && isEmpty ? 'text-[#FF742A]' : undefined
            )}
            sx={{
              ...customStyles.sliderStyles,
              // Only highlight slider track/thumb if not in section or not empty
              ...(isAmbientForm &&
                isEmpty &&
                !isInSection && {
                  '& .MuiSlider-thumb': {
                    color: highlightStyles.color,
                  },
                  '& .MuiSlider-track': {
                    color: highlightStyles.color,
                  },
                  '& .MuiSlider-rail': {
                    color: highlightStyles.color,
                    opacity: 0.3,
                  },
                }),
            }}
          />
        );
      }}
    />
  );
};

export const TextField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  customStyles,
  isAmbientForm,
}) => {
  const numberField = field as INumberField | ITextField;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: formField }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : formField.value;
        const maxWidthClass = customStyles?.fieldMaxWidth
          ? `max-w-[${customStyles.fieldMaxWidth}]`
          : undefined;

        const isEmpty = isFieldEmpty(fieldValue, field);
        const highlightStyles = getHighlightStyles(
          isAmbientForm || false,
          isEmpty
        );

        return (
          <AppTextField
            {...formField}
            value={fieldValue}
            type={numberField.type}
            label={numberField.label}
            placeholder={numberField.label}
            required={numberField.required}
            error={!!errors?.[name]}
            disabled={readonly}
            initiallyReadonly
            className={cn(maxWidthClass)}
            sx={{
              ...(isAmbientForm &&
                isEmpty && {
                  '& .MuiInputLabel-root': {
                    color: highlightStyles.color,
                  },
                }),
            }}
          />
        );
      }}
    />
  );
};

export const ConditionalField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  watch,
  readonly,
  isAmbientForm,
  setValue,
}) => {
  const conditionalField = field as IConditionalField;
  const currentValue = watch?.(name);

  // Get the main value from field or form with priority handling
  const fieldValue = (field as any).value;
  let mainValue = '';
  let subFieldValues: Record<string, any> = {};

  // Priority 1: If field.value is an object
  if (fieldValue && typeof fieldValue === 'object') {
    if ('value' in fieldValue) {
      // Standard shape: { value: "Yes", subFieldId: "2" }
      mainValue = (fieldValue as any).value || '';
      subFieldValues = fieldValue as Record<string, any>;
    } else {
      // Fallback shape from backend: only subField values present (e.g. { tea_cups_per_day: "2" })
      // Try to infer the main value from conditions based on which subField id is present
      for (const cond of conditionalField.conditions) {
        const subId = cond.subField?.id;
        if (subId && (fieldValue as any)[subId] !== undefined) {
          mainValue = cond.label || '';
          subFieldValues = fieldValue as Record<string, any>;
          break;
        }
      }
    }
  }
  // Priority 2: If field.value is a string like "Yes"
  else if (typeof fieldValue === 'string') {
    mainValue = fieldValue || '';
    // When it's a string, we need to look up the condition to get subField values
    const conditionForStringValue = conditionalField.conditions.find(
      (cond) => cond.label === mainValue
    );
    if (
      conditionForStringValue?.subField &&
      conditionForStringValue.subField.value
    ) {
      subFieldValues[conditionForStringValue.subField.id] =
        conditionForStringValue.subField.value;
    }
  }

  // Also support manual-entry patterns where subfield values are stored on the
  // field object as value_<subFieldId>, e.g. value_tea_cups_per_day: "4"
  // even when field.value only contains the main selection.
  conditionalField.conditions.forEach((cond) => {
    const subId = cond.subField?.id;
    if (!subId) return;

    const manualKey = `value_${subId}`;
    const manualValue = (field as any)[manualKey];

    if (
      manualValue !== undefined &&
      manualValue !== null &&
      manualValue !== ''
    ) {
      subFieldValues[subId] = manualValue;
    }
  });

  // Fallback: If we still don't have a mainValue, but one of the conditions
  // has a subField with a concrete value (e.g. tea_cups_per_day: "2"),
  // infer the main selection from that condition so the UI can show it.
  if (!mainValue) {
    const conditionWithSubFieldValue = conditionalField.conditions.find(
      (cond) =>
        cond.subField &&
        cond.subField.id &&
        cond.subField.value !== undefined &&
        cond.subField.value !== null &&
        cond.subField.value !== ''
    );

    if (conditionWithSubFieldValue && conditionWithSubFieldValue.subField) {
      mainValue = conditionWithSubFieldValue.label ?? '';
      subFieldValues = {
        ...subFieldValues,
        [conditionWithSubFieldValue.subField.id]:
          conditionWithSubFieldValue.subField.value,
      };
    }
  }

  // Get form values (highest priority)
  const formValue =
    typeof currentValue === 'string' ? currentValue : currentValue?.value;
  if (formValue) {
    mainValue = formValue;
  }

  // If we have an object from form, merge subField values
  if (
    currentValue &&
    typeof currentValue === 'object' &&
    'value' in currentValue
  ) {
    subFieldValues = { ...subFieldValues, ...currentValue };
  }

  const selectedCondition = conditionalField.conditions.find(
    (cond) => cond.label === mainValue
  );

  const renderAdditionField = useCallback(
    (optionLabel: string) => {
      if (!selectedCondition?.subField) return null;
      const {
        label,
        value: subFieldDefaultValue,
        ...subField
      } = selectedCondition.subField;
      const inputLabel = label;
      // Use a simple, unique subField name to avoid conflicts
      const subFieldName = `${name}_${selectedCondition.subField.id}`;
      const subFieldId = selectedCondition.subField.id;

      if (optionLabel === selectedCondition.label) {
        // Priority order for subField values:
        // 1. Field.value object (highest priority): subFieldValues[subFieldId]
        // 2. Form state: watch?.(subFieldName)
        // 3. Conditions default: subFieldDefaultValue
        // 4. Empty string (fallback)

        const currentSubFieldValue = watch?.(subFieldName);
        const objectSubFieldValue = subFieldId
          ? subFieldValues[subFieldId]
          : undefined;

        const subFieldValue =
          objectSubFieldValue !== undefined
            ? objectSubFieldValue
            : currentSubFieldValue !== undefined
              ? currentSubFieldValue
              : subFieldDefaultValue || '';

        // In edit mode, if we have a concrete subfield value from backend/config
        // but the form state is still empty, initialize the form value so the
        // input shows it.
        if (
          !readonly &&
          setValue &&
          currentSubFieldValue === undefined &&
          subFieldValue !== undefined &&
          subFieldValue !== null &&
          subFieldValue !== ''
        ) {
          setValue(subFieldName, subFieldValue);
        }

        return (
          <div className="flex gap-base items-center">
            <span className="whitespace-nowrap font-semibold">
              {inputLabel}
            </span>
            <TextField
              name={subFieldName}
              field={{
                ...subField,
                value: subFieldValue,
              }}
              control={control}
              readonly={readonly}
              customStyles={subField.customStyles}
              isAmbientForm={isAmbientForm}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    [
      selectedCondition,
      control,
      name,
      readonly,
      watch,
      subFieldValues,
      isAmbientForm,
      setValue,
    ]
  );

  if (readonly) {
    if (!mainValue) return null;

    if (mainValue === 'Yes') {
      // Use priority-based subField value in readonly mode too
      const subFieldId = selectedCondition?.subField?.id;
      const objectSubFieldValue = subFieldId
        ? subFieldValues[subFieldId]
        : undefined;
      const defaultSubFieldValue = selectedCondition?.subField?.value;
      const subFieldValueRaw = objectSubFieldValue || defaultSubFieldValue;
      const subFieldValue = formatDisplayValue(subFieldValueRaw);

      return (
        <span className="text-base">
          I drink {subFieldValue || '0'} cups of {field.label}
        </span>
      );
    } else if (mainValue === 'No') {
      return (
        <span className="text-base">No, I don&apos;t drink {field.label}</span>
      );
    }
    return null;
  }

  const isEmpty = isFieldEmpty(mainValue, field);
  const highlightStyles = getHighlightStyles(isAmbientForm || false, isEmpty);

  return (
    <div
      className={cn(
        'space-y-4',
        conditionalField.customStyles?.wrapperClassName
      )}
    >
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, value } }) => {
          // Use the main value for display
          const displayValue = mainValue || value || '';

          return (
            <AppRadio
              name={name}
              value={displayValue}
              onChange={(newValue) => {
                // Handle both string and object value changes
                if (typeof newValue === 'string') {
                  onChange({ value: newValue });
                } else {
                  onChange(newValue);
                }
              }}
              label={field.label}
              options={conditionalField.conditions.map((cond) => ({
                value: cond.label ?? '',
                label: (
                  <div className="flex gap-10 items-center">
                    {cond.label}
                    {renderAdditionField(cond?.label ?? '')}
                  </div>
                ),
              }))}
              disabled={readonly}
              errors={errors?.[name]}
              required={field.required}
              formControlProps={{
                sx: {
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'row',
                  gap: 2,
                  ['& .app-input-label']: {
                    minWidth: 200,
                    ...(isAmbientForm &&
                      isEmpty && {
                        color: highlightStyles.color,
                      }),
                  },
                  ...(isAmbientForm &&
                    isEmpty && {
                      '& .MuiRadio-root': {
                        color: highlightStyles.color,
                      },
                      '& .MuiFormControlLabel-label': {
                        color: highlightStyles.color,
                      },
                    }),
                },
              }}
            />
          );
        }}
      />
    </div>
  );
};

export const TextareaField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors: _errors,
  readonly,
  isAmbientForm,
  watch,
  setValue,
}) => {
  const isPhysicalChangeField =
    field.label?.toLowerCase().includes('physical change') ||
    /^physical change \d+$/i.test(field.label || '');

  const isNutritionChangeField =
    field.label?.toLowerCase().includes('nutrition change') ||
    field.label?.toLowerCase().includes('dietary change') ||
    field.label?.toLowerCase().includes('nutritional change') ||
    /^nutrition change \d+$/i.test(field.label || '');

  const isChangeField = isPhysicalChangeField || isNutritionChangeField;
  const minRows = isChangeField ? 1 : 3;

  const placeholder = isChangeField ? '' : field.label;

  const directValue = (field as any).value;
  const formValue = watch ? watch(name) : undefined;

  // Initialize form value if direct value exists and form value is empty
  React.useEffect(() => {
    if (
      !readonly &&
      setValue &&
      directValue !== undefined &&
      directValue !== null &&
      directValue !== '' &&
      (formValue === undefined || formValue === null || formValue === '')
    ) {
      const lowerDirectValue = String(directValue).toLowerCase().trim();
      if (
        !lowerDirectValue.includes('no information found') &&
        !lowerDirectValue.includes('not found') &&
        !lowerDirectValue.includes('no data') &&
        !lowerDirectValue.includes('unknown')
      ) {
        setValue(name, directValue);
      }
    }
  }, [directValue, formValue, name, setValue, readonly]);

  // Helper function to resolve field value from multiple sources
  const resolveFieldValue = (formVal: any, directVal: any): any => {
    // Priority 1: Form value (from nested structure or form state)
    if (formVal !== undefined && formVal !== null && formVal !== '') {
      // Check if it's a valid value (not "No information found" etc.)
      if (typeof formVal === 'string') {
        const lowerValue = formVal.toLowerCase().trim();
        if (
          !lowerValue.includes('no information found') &&
          !lowerValue.includes('not found') &&
          !lowerValue.includes('no data') &&
          !lowerValue.includes('unknown')
        ) {
          return formVal;
        }
      } else {
        return formVal;
      }
    }

    // Priority 2: Direct field value
    if (directVal !== undefined && directVal !== null && directVal !== '') {
      if (typeof directVal === 'string') {
        const lowerValue = directVal.toLowerCase().trim();
        if (
          !lowerValue.includes('no information found') &&
          !lowerValue.includes('not found') &&
          !lowerValue.includes('no data') &&
          !lowerValue.includes('unknown')
        ) {
          return directVal;
        }
      } else {
        return directVal;
      }
    }

    return formVal ?? directVal ?? '';
  };

  return (
    <div className="space-y-2">
      <Controller
        name={name}
        control={control}
        defaultValue={resolveFieldValue(formValue, directValue)}
        render={({ field: controllerField }) => {
          // Resolve value: check form value first, then direct field value
          const currentFormValue = controllerField.value;
          const resolvedValue = resolveFieldValue(
            currentFormValue,
            directValue
          );

          const currentFieldValue = readonly ? resolvedValue : resolvedValue;

          const isEmpty = isFieldEmpty(currentFieldValue, field);
          const highlightStyles = getHighlightStyles(
            isAmbientForm || false,
            isEmpty
          );

          if (isChangeField) {
            return (
              <>
                <label
                  className={`block text-sm text-gray-700 ${readonly ? 'font-bold' : 'font-medium'}`}
                  style={{ color: highlightStyles.color || 'inherit' }}
                >
                  {field.label}
                </label>
                {readonly ? (
                  <div
                    className="text-sm text-gray-900 leading-relaxed"
                    style={{ color: highlightStyles.color || 'inherit' }}
                  >
                    {currentFieldValue || 'No content provided'}
                  </div>
                ) : (
                  <div className="relative">
                    <TextareaAutosize
                      {...controllerField}
                      value={currentFieldValue || ''}
                      onChange={(e) => {
                        controllerField.onChange(e);
                      }}
                      placeholder={placeholder}
                      minRows={minRows}
                      disabled={readonly}
                      style={{
                        width: '100%',
                        padding: '8px 40px 8px 8px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        fontFamily: 'inherit',
                        fontSize: '14px',
                        resize: 'none',
                        backgroundColor: 'transparent',
                        color: 'inherit',
                      }}
                    />
                    <div className="absolute right-2 top-2 pointer-events-none">
                      <PenIcon className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                )}
              </>
            );
          }

          return (
            <TextareaAutosize
              {...controllerField}
              value={currentFieldValue || ''}
              onChange={(e) => {
                controllerField.onChange(e);
              }}
              placeholder={placeholder}
              minRows={minRows}
              disabled={readonly}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontFamily: 'inherit',
                fontSize: '14px',
                resize: 'vertical',
                backgroundColor: 'transparent',
                color: 'inherit',
              }}
            />
          );
        }}
      />
    </div>
  );
};

export const FieldGroup: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  readonly,
  isAmbientForm,
}) => {
  const { fields = [] } = field as any;

  if (!fields || fields.length === 0) {
    return null;
  }

  if (readonly) {
    return (
      <Box
        sx={{
          border: '1px solid #E2E8F0',
          borderRadius: '8px',

          backgroundColor: 'white',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: 10,
          }}
        >
          {fields.map((subField: any) => (
            <Controller
              key={subField.id}
              name={`${name}.${subField.id}`}
              control={control}
              render={({ field: controllerField }) => {
                // Determine the current value source: form value first, then field_group value, then subField value
                const fieldGroupValue = (field as any).value;
                const currentValue =
                  controllerField.value !== undefined
                    ? controllerField.value
                    : (fieldGroupValue && fieldGroupValue[subField.id]) !==
                        undefined
                      ? fieldGroupValue[subField.id]
                      : (subField as any).value || '';

                const isNoInformationFound =
                  typeof currentValue === 'string' &&
                  currentValue.toLowerCase().trim() === 'no information found';

                const displayValue = isNoInformationFound
                  ? '-'
                  : currentValue || '-';

                const isEmpty = isFieldEmpty(currentValue, subField);
                const highlightStyles = getHighlightStyles(
                  isAmbientForm || false,
                  isEmpty
                );

                const hasRedHighlight = isNoInformationFound;

                return (
                  <Box
                    sx={{
                      borderRight: hasRedHighlight
                        ? '1px solid #dc2626 !important'
                        : '1px solid #E2E8F0',
                      borderTop: hasRedHighlight
                        ? '1px solid #dc2626 !important'
                        : 'none',
                      borderBottom: hasRedHighlight
                        ? '1px solid #dc2626 !important'
                        : 'none',
                      borderLeft: hasRedHighlight
                        ? '1px solid #dc2626 !important'
                        : 'none',
                      borderRadius: hasRedHighlight ? '4px' : '0',
                      p: 3,
                      // mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      justifyContent: 'center',
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: highlightStyles.color || 'inherit',
                      }}
                    >
                      {subField.label}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: highlightStyles.color || 'inherit',
                        textAlign: hasRedHighlight ? 'center' : 'inherit',
                        flex: hasRedHighlight ? 1 : 'inherit',
                        display: hasRedHighlight ? 'flex' : 'inherit',
                        justifyContent: hasRedHighlight ? 'center' : 'inherit',
                      }}
                    >
                      {displayValue}
                    </Typography>
                  </Box>
                );
              }}
            />
          ))}
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        border: '1px solid #E2E8F0',
        borderRadius: '8px',
        p: 2,
        mb: 2,
        backgroundColor: 'white',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          gap: 5,
          justifyContent: 'space-between',
        }}
      >
        {fields.map((subField: any) => (
          <Box
            key={subField.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
            }}
          >
            <Controller
              name={`${name}.${subField.id}`}
              control={control}
              render={({ field: controllerField, fieldState: { error } }) => {
                const fieldGroupValue = (field as any).value;
                const currentValue =
                  controllerField.value !== undefined
                    ? controllerField.value
                    : (fieldGroupValue && fieldGroupValue[subField.id]) !==
                        undefined
                      ? fieldGroupValue[subField.id]
                      : (subField as any).value || '';

                const isNoInformationFound =
                  typeof currentValue === 'string' &&
                  currentValue.toLowerCase().trim() === 'no information found';

                const isEmpty = isFieldEmpty(controllerField.value, subField);

                const hasRedHighlight = isNoInformationFound;

                return (
                  <>
                    <Typography
                      variant="body1"
                      sx={{
                        fontSize: '16px',
                        fontWeight: 600,
                        color:
                          isAmbientForm && isEmpty ? '#FF742A' : 'text.primary',
                      }}
                    >
                      {subField.label}
                    </Typography>
                    <Box sx={{ flex: 1 }}>
                      {(() => {
                        const highlightStyles = getHighlightStyles(
                          isAmbientForm || false,
                          isEmpty
                        );

                        return (
                          <AppTextField
                            {...controllerField}
                            value={
                              isNoInformationFound ? '-' : controllerField.value
                            }
                            InputProps={{
                              readOnly: isNoInformationFound,
                            }}
                            type={
                              subField.type === 'number'
                                ? 'text'
                                : subField.type
                            }
                            onKeyDown={
                              subField.type === 'number'
                                ? (e: React.KeyboardEvent<HTMLDivElement>) =>
                                    allowNumbersWithDecimals(
                                      e as React.KeyboardEvent<HTMLInputElement>
                                    )
                                : undefined
                            }
                            inputProps={{
                              inputMode:
                                subField.type === 'number' ? 'decimal' : 'text',
                              pattern:
                                subField.type === 'number'
                                  ? '[0-9]*(\\.[0-9]+)?'
                                  : undefined,
                              min: subField.min,
                              step: subField.step,
                            }}
                            error={!!error}
                            helperText={error?.message}
                            disabled={readonly}
                            sx={{
                              maxWidth: 100,
                              ...(hasRedHighlight && {
                                '& .MuiOutlinedInput-root': {
                                  borderColor: '#dc2626 !important',
                                  '&:hover': {
                                    borderColor: '#dc2626 !important',
                                  },
                                  '&.Mui-focused': {
                                    borderColor: '#dc2626 !important',
                                  },
                                  '& .MuiInputBase-input': {
                                    textAlign: 'center',
                                  },
                                },
                                '& .MuiInputBase-input::placeholder': {
                                  opacity: 1,
                                  textAlign: 'center',
                                },
                              }),
                              ...(isAmbientForm &&
                                isEmpty &&
                                !hasRedHighlight && {
                                  '& .MuiInputLabel-root': {
                                    color: highlightStyles.color,
                                  },
                                }),
                            }}
                            size="small"
                          />
                        );
                      })()}
                    </Box>
                  </>
                );
              }}
            />
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export const SectionField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  watch,
  setValue,
  readonly,
  isAmbientForm,
}) => {
  const sectionField = field as ISectionField;

  const isReasonSection = field.label
    ?.toLowerCase()
    .includes('reason for your choice');

  const isKnowledgeSection =
    field.label?.toLowerCase().includes('knowledge') ||
    field.label?.toLowerCase().includes('benefit of exercise') ||
    field.label?.toLowerCase().includes('examples of') ||
    field.label?.toLowerCase().includes('training') ||
    field.label?.toLowerCase().includes('exercise') ||
    field.label?.toLowerCase().includes('walking') ||
    field.label?.toLowerCase().includes('material') ||
    field.label?.toLowerCase().includes('food') ||
    field.label?.toLowerCase().includes('minimum') ||
    field.label?.toLowerCase().includes('which') ||
    field.label?.toLowerCase().includes('what is');

  if ((isReasonSection || isKnowledgeSection) && sectionField.fields) {
    return (
      <div className={isKnowledgeSection ? 'space-y-1' : 'space-y-4'}>
        <h4
          className={`font-medium ${isKnowledgeSection ? 'text-sm mb-2' : 'text-base'}`}
          style={{
            ...(isReasonSection && { color: 'inherit' }),
          }}
        >
          {field.label}
        </h4>
        <div
          className={`${isKnowledgeSection ? 'bg-white border border-gray-50 rounded-lg overflow-hidden ring-1 ring-gray-50 ml-4' : 'rounded-lg overflow-hidden border border-gray-50'}`}
        >
          {sectionField.fields.map((subField, index) => {
            const fieldName = `${name}.fields.${index}.value`;
            const radioField = subField as IRadioField;

            const whiteBackgroundLabels = [
              'Availability of Gyms',
              'Subjective Feeling of Gym Workouts',
              'Social Support for Gym Attendance',
              'Availability of healthy food',
              'Subjective Feeling',
              'Social Support',
            ];
            const isWhiteBackground =
              whiteBackgroundLabels.includes(subField.label || '') ||
              whiteBackgroundLabels.includes(field.label || '');
            const rowBgColor = isWhiteBackground
              ? 'bg-white'
              : readonly
                ? 'bg-[#DAE1E7]'
                : 'bg-sky-100';

            return (
              <div
                key={subField.id}
                className={`${isKnowledgeSection ? 'grid grid-cols-5 gap-0 border-b border-gray-50 last:border-b-0 hover:bg-gray-50 divide-x divide-gray-200' : `grid grid-cols-3 border-b border-gray-50 last:border-b-0 divide-x divide-gray-200 ${rowBgColor}`}`}
                style={
                  isKnowledgeSection
                    ? { display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)' }
                    : undefined
                }
              >
                {isKnowledgeSection ? (
                  <>
                    <div className="p-2 flex items-start">
                      <span className="text-sm text-gray-700 leading-relaxed break-words">
                        {subField.label}
                      </span>
                    </div>

                    {[0, 1, 2, 3].map((optionIndex) => {
                      const option = radioField.options?.[optionIndex];
                      return (
                        <div
                          key={optionIndex}
                          className="p-2 flex items-center justify-center"
                        >
                          {option && (
                            <Controller
                              name={fieldName}
                              control={control}
                              render={({ field: { onChange, value } }) => {
                                // For ambient listening data, check multiple possible value sources
                                const fieldValue =
                                  value || // Check the form value first for editability
                                  (subField as any).value || // Then check the autofilled value from ambient data
                                  ''; // Default to empty string if neither exists

                                const isEmpty = isFieldEmpty(
                                  fieldValue,
                                  subField
                                );
                                const highlightStyles = getHighlightStyles(
                                  isAmbientForm || false,
                                  isEmpty
                                );

                                return (
                                  <label className="flex items-center space-x-1.5 cursor-pointer">
                                    <input
                                      type="radio"
                                      value={option}
                                      checked={
                                        String(fieldValue ?? '')
                                          .trim()
                                          .toLowerCase() ===
                                        String(option ?? '')
                                          .trim()
                                          .toLowerCase()
                                      }
                                      onChange={() => onChange(option)}
                                      readOnly={readonly}
                                      aria-disabled={readonly}
                                      tabIndex={readonly ? -1 : 0}
                                      className={`w-3.5 h-3.5 border-gray-300 focus:ring-black-900 !text-black !accent-black ${readonly ? 'pointer-events-none' : ''}`}
                                      style={{
                                        accentColor:
                                          highlightStyles.color || '#000000',
                                      }}
                                    />
                                    <span
                                      className="text-sm text-gray-700"
                                      style={{
                                        color:
                                          highlightStyles.color || 'inherit',
                                      }}
                                    >
                                      {option}
                                    </span>
                                  </label>
                                );
                              }}
                            />
                          )}
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <>
                    <Controller
                      name={fieldName}
                      control={control}
                      render={({ field: { onChange, value } }) => {
                        const fieldValue =
                          value || (subField as any).value || '';

                        const isEmpty = isFieldEmpty(fieldValue, subField);
                        const highlightStyles = getHighlightStyles(
                          isAmbientForm || false,
                          isEmpty
                        );

                        return (
                          <>
                            <div
                              className={`p-3 flex items-center border-r border-gray-700 ${rowBgColor}`}
                            >
                              <span
                                className="font-normal text-sm"
                                style={{
                                  color: highlightStyles.color || 'inherit',
                                }}
                              >
                                {subField.label}
                              </span>
                            </div>

                            <div className="col-span-2 grid grid-cols-2">
                              {radioField.options?.map(
                                (option, optionIndex) => (
                                  <div
                                    key={option}
                                    className={`p-3 flex items-center ${optionIndex < (radioField.options?.length || 0) - 1 ? 'border-r border-black' : ''}`}
                                  >
                                    <label className="flex items-center space-x-2 cursor-pointer">
                                      <input
                                        type="radio"
                                        value={option}
                                        checked={
                                          String(fieldValue ?? '')
                                            .trim()
                                            .toLowerCase() ===
                                          String(option ?? '')
                                            .trim()
                                            .toLowerCase()
                                        }
                                        onChange={() => onChange(option)}
                                        readOnly={readonly}
                                        aria-disabled={readonly}
                                        tabIndex={readonly ? -1 : 0}
                                        className={`w-4 h-4 border-black focus:ring-black !text-black !accent-black ${readonly ? 'pointer-events-none' : ''}`}
                                        style={{
                                          color:
                                            highlightStyles.color || '#000000',
                                          accentColor:
                                            highlightStyles.color || '#000000',
                                        }}
                                      />
                                      <span
                                        className="text-sm"
                                        style={{
                                          color:
                                            highlightStyles.color || 'inherit',
                                        }}
                                      >
                                        {option}
                                      </span>
                                    </label>
                                  </div>
                                )
                              )}
                            </div>
                          </>
                        );
                      }}
                    />
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  const isPhysicalChangesSection = field.label
    ?.toLowerCase()
    .includes('rank the top 3 physical changes');

  const isNutritionAttitudeSection =
    field.label?.toLowerCase().includes('physical change') ||
    field.label?.toLowerCase().includes('nutrition change') ||
    field.label?.toLowerCase().includes('nutritional change') ||
    field.label?.toLowerCase().includes('dietary change') ||
    (sectionField.fields &&
      sectionField.fields.length >= 3 &&
      sectionField.fields[0]?.type === 'textarea' &&
      sectionField.fields[1]?.label?.toLowerCase().includes('important') &&
      sectionField.fields[2]?.label?.toLowerCase().includes('confident'));

  if (
    (isPhysicalChangesSection || isNutritionAttitudeSection) &&
    sectionField.fields
  ) {
    return (
      <div className="space-y-4">
        <h4 className="text-base font-medium">{field.label}</h4>
        <div className="space-y-6">
          {sectionField.fields &&
            Array.from(
              { length: Math.ceil(sectionField.fields.length / 3) },
              (_, groupIndex) => {
                const startIndex = groupIndex * 3;
                const textareaField = sectionField.fields![startIndex];
                const importanceField = sectionField.fields![startIndex + 1];
                const confidenceField = sectionField.fields![startIndex + 2];

                if (!textareaField || !importanceField || !confidenceField)
                  return null;

                const TextareaComponent = fieldComponents[textareaField.type];
                const ImportanceComponent =
                  fieldComponents[importanceField.type];
                const ConfidenceComponent =
                  fieldComponents[confidenceField.type];

                if (
                  !TextareaComponent ||
                  !ImportanceComponent ||
                  !ConfidenceComponent
                ) {
                  return null;
                }

                const isNutritionSection =
                  field.label?.toLowerCase().includes('nutrition') ||
                  field.label?.toLowerCase().includes('dietary');

                const isPhysicalSection = field.label
                  ?.toLowerCase()
                  .includes('physical');

                // const numberedLabel = isNutritionSection
                //   ? `Nutrition Change ${groupIndex + 1}`
                //   : isPhysicalSection
                //     ? `Physical Change ${groupIndex + 1}`
                //     : textareaField.label;

                return (
                  <div key={`group-${groupIndex}`}>
                    {/* Add grey horizontal line before sections 2 and 3 */}
                    {groupIndex > 0 &&
                      (isNutritionSection || isPhysicalSection) && (
                        <div className="border-t border-gray-300 my-6"></div>
                      )}
                    <div className="space-y-3">
                      <div>
                        <TextareaComponent
                          name={`${name}.fields.${startIndex}.value`}
                          field={{
                            ...textareaField,
                            // label: numberedLabel,
                          }}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          readonly={readonly}
                          isAmbientForm={isAmbientForm}
                        />
                      </div>

                      {(() => {
                        // Check if importance and confidence fields have values
                        const importanceFieldName = `${name}.fields.${startIndex + 1}.value`;
                        const confidenceFieldName = `${name}.fields.${startIndex + 2}.value`;
                        const importanceFormValue = watch
                          ? watch(importanceFieldName)
                          : undefined;
                        const confidenceFormValue = watch
                          ? watch(confidenceFieldName)
                          : undefined;

                        // Resolve values: check form value first, then direct field value
                        const importanceDirectValue = (importanceField as any)
                          .value;
                        const confidenceDirectValue = (confidenceField as any)
                          .value;

                        const resolveValue = (formVal: any, directVal: any) => {
                          if (
                            formVal !== undefined &&
                            formVal !== null &&
                            formVal !== ''
                          ) {
                            if (typeof formVal === 'number') return formVal;
                            if (typeof formVal === 'string') {
                              const lower = formVal.toLowerCase().trim();
                              if (
                                !lower.includes('no information found') &&
                                !lower.includes('not found') &&
                                !lower.includes('no data') &&
                                !lower.includes('unknown') &&
                                lower !== ''
                              ) {
                                const num = Number(formVal);
                                if (!isNaN(num)) return num;
                              }
                            }
                          }
                          if (
                            directVal !== undefined &&
                            directVal !== null &&
                            directVal !== ''
                          ) {
                            if (typeof directVal === 'number') return directVal;
                            if (typeof directVal === 'string') {
                              const lower = directVal.toLowerCase().trim();
                              if (
                                !lower.includes('no information found') &&
                                !lower.includes('not found') &&
                                !lower.includes('no data') &&
                                !lower.includes('unknown') &&
                                lower !== ''
                              ) {
                                const num = Number(directVal);
                                if (!isNaN(num)) return num;
                              }
                            }
                          }
                          return formVal ?? directVal ?? undefined;
                        };

                        const importanceValue = resolveValue(
                          importanceFormValue,
                          importanceDirectValue
                        );
                        const confidenceValue = resolveValue(
                          confidenceFormValue,
                          confidenceDirectValue
                        );

                        const importanceEmpty = isFieldEmpty(
                          importanceValue,
                          importanceField
                        );
                        const confidenceEmpty = isFieldEmpty(
                          confidenceValue,
                          confidenceField
                        );

                        const importanceHighlight =
                          isAmbientForm && importanceEmpty
                            ? '#FF742A'
                            : undefined;
                        const confidenceHighlight =
                          isAmbientForm && confidenceEmpty
                            ? '#FF742A'
                            : undefined;

                        return (
                          <>
                            <div className="grid grid-cols-2 gap-4">
                              <div
                                className="text-sm font-medium text-gray-700"
                                style={{
                                  color: importanceHighlight || 'inherit',
                                }}
                              >
                                {importanceField.label}
                              </div>
                              <div
                                className="text-sm font-medium text-gray-700"
                                style={{
                                  color: confidenceHighlight || 'inherit',
                                }}
                              >
                                {confidenceField.label}
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <ImportanceComponent
                                  name={importanceFieldName}
                                  field={{ ...importanceField, label: '' }}
                                  control={control}
                                  watch={watch}
                                  setValue={setValue}
                                  readonly={readonly}
                                  isAmbientForm={isAmbientForm}
                                />
                                {isPhysicalSection && (
                                  <div className="text-xs text-gray-500 mt-1 text-left">
                                    (0=not important to 10=very important)
                                  </div>
                                )}
                              </div>
                              <div>
                                <ConfidenceComponent
                                  name={confidenceFieldName}
                                  field={{ ...confidenceField, label: '' }}
                                  control={control}
                                  watch={watch}
                                  setValue={setValue}
                                  readonly={readonly}
                                  isAmbientForm={isAmbientForm}
                                />
                                {isPhysicalSection && (
                                  <div className="text-xs text-gray-500 mt-1 text-left">
                                    (0=not confident to 10=very confident)
                                  </div>
                                )}
                              </div>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                );
              }
            )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4
        className="text-base font-medium"
        style={{
          color:
            isAmbientForm &&
            sectionField.fields?.some((subField: any, idx: number) => {
              const fieldValue = watch
                ? watch(`${name}.fields.${idx}.value`)
                : (subField as any).value;
              return isFieldEmpty(fieldValue, subField);
            })
              ? '#FF742A'
              : 'inherit',
        }}
      >
        {field.label}
      </h4>
      {sectionField.fields && sectionField.fields.length > 0 && (
        <div className="space-y-4">
          {sectionField.fields.map((subField, index) => {
            const FieldComponent = fieldComponents[subField.type];
            if (!FieldComponent) {
              console.warn(
                `No renderer found for field type: ${subField.type}`
              );
              return null;
            }

            const fieldName =
              subField.type === 'table'
                ? `${name}.fields.${index}`
                : `${name}.fields.${index}.value`;

            return (
              <div key={subField.id} className="ml-4">
                <FieldComponent
                  name={fieldName}
                  field={subField}
                  control={control}
                  watch={watch}
                  setValue={setValue}
                  readonly={readonly}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const ConditionalSelectField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  isAmbientForm,
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : value;

        const isEmpty = isFieldEmpty(fieldValue, field);
        const highlightStyles = getHighlightStyles(
          isAmbientForm || false,
          isEmpty
        );

        return (
          <AppTextField
            value={fieldValue}
            onChange={onChange}
            label={field.label}
            placeholder={field.label}
            required={field.required}
            error={!!errors?.[name]}
            disabled={readonly}
            initiallyReadonly
            sx={{
              ...(isAmbientForm &&
                isEmpty && {
                  '& .MuiInputLabel-root': {
                    color: highlightStyles.color,
                  },
                }),
            }}
          />
        );
      }}
    />
  );
};

export const fieldComponents: Record<
  LifestyleFields,
  React.FC<FieldComponentProps>
> = {
  radio: RadioField,
  slider: SliderField,
  number: TextField,
  conditional: ConditionalField,
  text: TextField,
  table: TableField,
  textarea: TextareaField,
  section: SectionField,
  grouped_table: GroupedTableField,
  time_range: TextField,
  conditional_select: ConditionalSelectField,
  frequency: Fragment as React.FC<FieldComponentProps>,
  searchable_select: TextField, // Using TextField as a fallback
  dependent_autofill: TextField, // Using TextField as a fallback
  field_group: FieldGroup,
} as const;
