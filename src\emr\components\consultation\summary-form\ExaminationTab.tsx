import React from 'react';

import { UseFormReturn, useWatch } from 'react-hook-form';

import EditableText from '@/lib/common/editable_text';
import TextInput from '@/lib/text_input';

import { SummarizeConversationRes } from '@/query/speech';

import {
  vitalSignFields,
  anthropometryFields,
  generalPhysicalExaminationFields,
  generalExaminationFields,
  GeneralPhysicalExaminationField,
  AnthropometryField,
  VitalField,
  SystemicExaminationField,
  systemicExaminationFields,
} from '@/utils/constants/consultation';
import { isEmptyHtmlContent } from '@/utils/empty-html-detection';

import { Switch } from '@/components/ui/switch';

type ExaminationTabProps = {
  editable: boolean;
  data: any;
  expanded: boolean;
  form: UseFormReturn<SummarizeConversationRes['summary']>;
  isAmbientForm?: boolean;
  handleChangeVitalSignField: (field: VitalField, newValue?: string) => void;
  handleChangeAnthropometryField: (
    field: AnthropometryField,
    newValue?: string
  ) => void;
  handleChangeGeneralPhysicalExaminationField: (
    field: GeneralPhysicalExaminationField,
    newValue: boolean | string
  ) => void;
  handleChangeHEENTField: (newValue?: string) => void;
  handleChangeSystemicExaminationField: (
    field: SystemicExaminationField,
    newValue?: string
  ) => void;
};

// Helper function to detect empty fields for ambient form highlighting
const isFieldEmpty = (value: any): boolean => {
  if (value === null || value === undefined || value === '') return true;
  if (typeof value === 'string') {
    // First check for empty HTML content patterns
    if (isEmptyHtmlContent(value)) {
      return true;
    }

    const trimmedValue = value.trim();
    if (trimmedValue === '') return true;
    // Check for common fallback phrases from ambient listening
    const fallbackPhrases = [
      'no information found',
      'no information available',
      'No specific details provided',
      'no information',
      '',
    ];
    if (
      fallbackPhrases.some((phrase) =>
        trimmedValue.toLowerCase().includes(phrase.toLowerCase())
      )
    )
      return true;

    // Treat numeric zero strings as empty (e.g., "0" or "0.0")
    const numericZeroMatch = /^0+(?:\.0+)?$/;
    if (numericZeroMatch.test(trimmedValue)) return true;

    return false;
  }
  if (typeof value === 'number') return value === 0;
  if (typeof value === 'boolean') return value === false;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') {
    if (!value) return true;
    // For anthropometry fields, check if all numeric values are 0 or empty strings
    if (
      'height' in value ||
      'weight' in value ||
      'bmi' in value ||
      'waistCircumference' in value
    ) {
      return Object.values(value).every(
        (val) =>
          val === '' ||
          val === 0 ||
          val === '0' ||
          val === null ||
          val === undefined
      );
    }
    return Object.keys(value).length === 0;
  }
  return false;
};

// Reusable FieldInput for vital/anthropometry fields
const FieldInput: React.FC<{
  label: string;
  unit?: string;
  value?: string;
  defaultValue?: string;
  readOnly?: boolean;
  editable?: boolean;
  onChange?: (val?: string) => void;
  className?: string;
  inputClassname?: string;
  isAmbientForm?: boolean;
  field?: string; // Add field name for specific checks
}> = ({
  label,
  unit,
  value,
  defaultValue,
  readOnly,
  editable,
  onChange,
  className = '',
  inputClassname = '',
  isAmbientForm = false,
  field,
}) => {
  const currentValue = value || defaultValue;

  const isEmpty = (() => {
    if (!field) return isFieldEmpty(currentValue);

    if (['height', 'weight', 'bmi', 'waistCircumference'].includes(field)) {
      return currentValue === '' || currentValue === '0' || !currentValue;
    }

    if (
      [
        'heartRate',
        'systolicPressure',
        'diastolicPressure',
        'respiratoryRate',
        'spO2',
        'temperature',
      ].includes(field)
    ) {
      if (!currentValue || currentValue === '') return true;

      const numValue = parseFloat(currentValue);

      if (isNaN(numValue) || numValue === 0) {
        return true;
      }

      return false;
    }

    return isFieldEmpty(currentValue);
  })();

  return (
    <div className={`flex flex-col w-[110px] min-w-[110px] ${className}`}>
      <span
        className="text-xs font-medium leading-tight mb-0.5 whitespace-nowrap overflow-visible break-words text-black"
        style={{
          whiteSpace: 'nowrap',
          overflow: 'visible',
          textOverflow: 'unset',
          color: isAmbientForm && isEmpty ? '#FF742A' : 'inherit',
        }}
      >
        {label}
      </span>
      <span
        className="block text-xs font-normal leading-tight mb-1 min-h-[14px] text-black"
        style={{ color: isAmbientForm && isEmpty ? '#FF742A' : 'inherit' }}
      >
        {unit || ''}
      </span>
      <TextInput
        label={undefined}
        className="w-full"
        inputClassname={`w-4/6 h-8 min-h-8 max-h-8 px-2 py-1 text-xs ${!editable ? 'read-only:bg-[#E8EBED] bg-[#E8EBED]' : 'bg-white'} ${inputClassname}`}
        variant={'bordered'}
        readOnly={readOnly}
        defaultValue={defaultValue}
        value={value}
        onChange={onChange}
      />
    </div>
  );
};

const ExaminationTab: React.FC<ExaminationTabProps> = ({
  editable,
  data,
  expanded,
  form,
  isAmbientForm,
  handleChangeVitalSignField,
  handleChangeAnthropometryField,
  handleChangeGeneralPhysicalExaminationField,
  handleChangeHEENTField,
  handleChangeSystemicExaminationField,
}) => {
  const anthropometry = useWatch({
    control: form.control,
    name: 'anthropometry',
  });
  const generalPhysicalExamination = useWatch({
    control: form.control,
    name: 'generalPhysicalExamination',
  });
  const effectiveGeneralPhysicalExamination =
    generalPhysicalExamination ?? data?.summary?.generalPhysicalExamination;
  // Watch additional fields so the UI can react to manual edits (remove highlight)
  const vitals = useWatch({ control: form.control, name: 'vitals' });
  const heent = useWatch({ control: form.control, name: 'heent' });
  const systemicExamination = useWatch({
    control: form.control,
    name: 'systemicExamination',
  });

  const heentEmpty = isFieldEmpty((heent ?? data?.summary?.heent) as any);

  return (
    <div className="flex flex-col gap-1.5">
      <section>
        <h3 className="font-medium text-sm text-[#001926] mb-2">Vital Signs</h3>
        <div className="flex flex-wrap gap-3 mt-2 items-center">
          {vitalSignFields.map((field) => {
            const ambientValue =
              data?.summary?.vitals?.[field.key as VitalField];
            const watched = vitals
              ? vitals[field.key as VitalField]
              : undefined;

            const getNumericValue = (val: any): number | null => {
              if (val == null) return null;
              if (typeof val === 'number') return val;
              if (
                typeof val === 'object' &&
                val.value != null &&
                typeof val.value === 'number'
              ) {
                return val.value;
              }
              return null;
            };

            const numericValue =
              watched != null
                ? getNumericValue(watched)
                : getNumericValue(ambientValue);

            const displayValue =
              numericValue != null && numericValue !== 0
                ? numericValue.toString()
                : '';

            const displayValueStr =
              displayValue !== undefined && displayValue !== null
                ? String(displayValue)
                : '';

            return (
              <FieldInput
                key={field.key}
                label={field.label}
                unit={field.unit}
                editable={editable}
                readOnly={!editable}
                defaultValue={displayValueStr}
                value={displayValueStr}
                field={field.key}
                isAmbientForm={isAmbientForm}
                onChange={(newValue) =>
                  handleChangeVitalSignField(field.key as VitalField, newValue)
                }
              />
            );
          })}
        </div>
      </section>
      <section>
        <h3 className="font-medium text-sm text-[#001926] mb-2">
          Anthropometry
        </h3>
        <div className="flex flex-nowrap gap-3 mt-2">
          {anthropometryFields.map((field) => (
            <FieldInput
              key={field.key}
              label={field.label}
              unit={field.unit}
              editable={editable}
              readOnly={!editable || field.key === 'bmi'}
              defaultValue={
                data?.summary?.anthropometry?.[
                  field.key as AnthropometryField
                ] != null
                  ? data?.summary?.anthropometry[
                      field.key as AnthropometryField
                    ]?.toString()
                  : ''
              }
              value={
                anthropometry &&
                `${anthropometry[field.key as AnthropometryField] || ''}`
              }
              field={field.key}
              isAmbientForm={isAmbientForm}
              onChange={(newValue) =>
                handleChangeAnthropometryField(
                  field.key as AnthropometryField,
                  newValue
                )
              }
            />
          ))}
        </div>
      </section>
      <section>
        <h3 className="font-medium text-sm text-[#001926] mb-2">
          General Physical Examination
        </h3>
        <div className="flex flex-wrap gap-3 mt-2 items-start">
          {/* Simple fields without notes */}
          {generalPhysicalExaminationFields.map((field) => {
            const isEnabled =
              (effectiveGeneralPhysicalExamination &&
                !!effectiveGeneralPhysicalExamination[
                  field.key as GeneralPhysicalExaminationField
                ]) ||
              false;
            return (
              <div
                key={field.key}
                className="flex flex-col gap-1 items-start mt-3"
              >
                <div className="flex gap-2 items-center">
                  <label
                    htmlFor={field.key}
                    className="text-sm whitespace-nowrap"
                    style={{
                      color: 'inherit',
                    }}
                  >
                    {field.label}
                  </label>
                  {editable || expanded ? (
                    <Switch
                      id={field.key}
                      disabled={!editable}
                      defaultChecked={isEnabled}
                      onCheckedChange={(checked) =>
                        handleChangeGeneralPhysicalExaminationField(
                          field.key as GeneralPhysicalExaminationField,
                          checked
                        )
                      }
                    />
                  ) : (
                    <span className="text-sm gap-2 flex">
                      <span>:</span>
                      <span>{isEnabled ? ' Yes' : ' No'}</span>
                    </span>
                  )}
                </div>
              </div>
            );
          })}

          {generalExaminationFields.map((field) => {
            const isEnabled =
              (effectiveGeneralPhysicalExamination &&
                !!effectiveGeneralPhysicalExamination[
                  field.key as GeneralPhysicalExaminationField
                ]) ||
              false;
            return (
              <div
                key={field.key}
                className={`flex flex-col  gap-1  rounded-lg p-3 w-auto ${editable ? 'bg-[#E6F6FF]' : ''}`}
              >
                <div className="flex gap-3 items-center">
                  <label
                    htmlFor={field.key}
                    className="text-sm whitespace-nowrap"
                    style={{
                      color: 'inherit',
                    }}
                  >
                    {field.label}
                  </label>
                  {editable || expanded ? (
                    <Switch
                      id={field.key}
                      disabled={!editable}
                      defaultChecked={isEnabled}
                      onCheckedChange={(checked) =>
                        handleChangeGeneralPhysicalExaminationField(
                          field.key as GeneralPhysicalExaminationField,
                          checked
                        )
                      }
                    />
                  ) : (
                    <span className="text-sm gap-2 flex">
                      <span>:</span>
                      <span>{isEnabled ? ' Yes' : ' No'}</span>
                    </span>
                  )}
                </div>
                {field.key === 'pedalEnema' &&
                  effectiveGeneralPhysicalExamination?.pedalEnema === true && (
                    <TextInput
                      className="mt-1 min-w-[120px] max-w-[180px]"
                      placeholder="Notes"
                      readOnly={!editable}
                      inputClassname={`h-8 px-2 py-1 text-xs ${editable ? 'bg-[#B4E5FE]' : 'read-only:bg-[#E8EBED]'}`}
                      defaultValue={
                        generalPhysicalExamination?.pedalEnemaNotes ||
                        data?.summary?.generalPhysicalExamination
                          ?.pedalEnemaNotes ||
                        ''
                      }
                      onChange={(newValue) =>
                        handleChangeGeneralPhysicalExaminationField(
                          'pedalEnemaNotes' as GeneralPhysicalExaminationField,
                          newValue as string
                        )
                      }
                    />
                  )}
                {field.key === 'lymphadenopathy' &&
                  effectiveGeneralPhysicalExamination?.lymphadenopathy ===
                    true && (
                    <TextInput
                      className="mt-1 w-[140px]"
                      placeholder="Notes"
                      readOnly={!editable}
                      inputClassname={`h-8 px-2 py-1 text-xs ${editable ? 'bg-[#B4E5FE]' : 'read-only:bg-[#E8EBED]'}`}
                      defaultValue={
                        generalPhysicalExamination?.lymphadenopathyNotes ||
                        data?.summary?.generalPhysicalExamination
                          ?.lymphadenopathyNotes ||
                        ''
                      }
                      onChange={(newValue) =>
                        handleChangeGeneralPhysicalExaminationField(
                          'lymphadenopathyNotes' as GeneralPhysicalExaminationField,
                          newValue as string
                        )
                      }
                    />
                  )}
              </div>
            );
          })}
        </div>
      </section>
      <section>
        <h3
          className="font-medium text-base text-[#001926]"
          style={{ color: isAmbientForm && heentEmpty ? '#FF742A' : 'inherit' }}
        >
          HEENT
        </h3>
        <EditableText
          editable={editable}
          bg={'white'}
          placeholder="Enter HEENT"
          contentClassName="min-h-[36px] pt-3 pb-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
          value={heent ?? data?.summary?.heent}
          isAmbientForm={isAmbientForm}
          onChange={handleChangeHEENTField}
          emptyPlaceholder={'Nil'}
        />
      </section>
      <section>
        <h3 className="font-medium text-base text-[#001926] mb-2">
          Systemic Examination
        </h3>
        {systemicExaminationFields.map((field) => (
          <EditableText
            key={field.key}
            className="mt-2.5"
            label={field.label}
            labelSize="Small"
            placeholder={field.placeholder}
            editable={editable}
            bg={'white'}
            emptyPlaceholder={'Nil'}
            contentClassName="min-h-[36px] pt-3 pb-2 max-h-[96px] overflow-y-auto border border-[#DAE1E7] bg-white rounded-md px-3 shadow-sm mb-2"
            value={
              (systemicExamination &&
                systemicExamination[field.key as SystemicExaminationField]) ??
              (data?.summary?.systemicExamination
                ? data?.summary?.systemicExamination[
                    field.key as SystemicExaminationField
                  ]
                : '')
            }
            isAmbientForm={isAmbientForm}
            onChange={(newValue) =>
              handleChangeSystemicExaminationField(
                field.key as SystemicExaminationField,
                newValue
              )
            }
          />
        ))}
      </section>
    </div>
  );
};

export default ExaminationTab;
