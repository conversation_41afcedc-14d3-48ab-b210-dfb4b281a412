import { <PERSON>, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useCallback } from 'react';

import { Button, Skeleton } from '@mui/material';
import { FaChevronDown } from 'react-icons/fa';

import { cn } from '@/lib/utils';

import { capitalizeFirstLetter } from '@/utils/string';

import { DoctorInfo } from '@/types/emr/doctor-profile/personal-info';

type Props = {
  open: boolean;
  handleClick?: MouseEventHandler<HTMLButtonElement>;
  name: string;
  userData: any;
  doctorProfile: DoctorInfo | null;
};

const MenuButton: FC<Props> = ({
  open,
  handleClick,
  name,
  userData,
  doctorProfile,
}) => {
  const renderUserName = useCallback(() => {
    if (!doctorProfile?.general?.fullName && !name) {
      return <Skeleton variant="text" className="w-25 font-medium bg-white" />;
    }
    return (
      <span className="font-medium text-sm text-white">
        {doctorProfile?.general?.fullName ?? name}
      </span>
    );
  }, [doctorProfile?.general?.fullName, name]);

  const renderUserRole = useCallback(() => {
    if (!doctorProfile?.general?.fullName && !name) {
      return <Skeleton variant="text" className="w-25 text-xs bg-white" />;
    }
    return (
      <span className="text-xs font-light text-white -tracking-[2.2%] -mt-0.5">
        {capitalizeFirstLetter(userData?.userRole) || ''}
      </span>
    );
  }, [doctorProfile?.general?.fullName, name, userData?.userRole]);

  return (
    <Button
      id="demo-positioned-button"
      aria-controls={open ? 'demo-positioned-menu' : undefined}
      aria-haspopup="true"
      aria-expanded={open ? 'true' : undefined}
      onClick={handleClick}
      className="flex pl-2"
      sx={{ color: 'white', textTransform: 'none' }}
      disableTouchRipple
    >
      <div className="flex flex-col text-right pr-2">
        {renderUserName()}
        {renderUserRole()}
      </div>
      <div
        className={cn('text-xs transition-transform duration-300', {
          'rotate-180': open,
        })}
      >
        <FaChevronDown />
      </div>
    </Button>
  );
};

export default memo(MenuButton);
