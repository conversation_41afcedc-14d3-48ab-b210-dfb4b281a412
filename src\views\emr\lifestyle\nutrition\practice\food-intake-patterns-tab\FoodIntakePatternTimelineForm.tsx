import { FC, memo, useEffect, useMemo, useCallback, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { Questionnaire } from '@/types/emr/lifestyle/questionnaire';

import FoodIntakePatternForm from './FoodIntakePatternForm';

type Props = {
  data: Questionnaire;
};
const FoodIntakePatternTimelineForm: FC<Props> = ({ data }) => {
  // Detect if this is from ambient listening
  const hasConversationInObject = useCallback(
    (obj: any, depth = 0): boolean => {
      if (!obj || depth > 3) return false;

      // Direct checks for common paths
      if (Array.isArray(obj.conversation) && obj.conversation.length > 0)
        return true;
      if (
        Array.isArray(obj?.summary?.conversation) &&
        obj.summary.conversation.length > 0
      )
        return true;
      if (
        Array.isArray(obj?.response?.conversation) &&
        obj.response.conversation.length > 0
      )
        return true;
      if (
        Array.isArray(obj?.response?.summary?.conversation) &&
        obj.response.summary.conversation.length > 0
      )
        return true;
      if (
        Array.isArray(obj?.response?.response?.conversation) &&
        obj.response.response.conversation.length > 0
      )
        return true;

      // Generic shallow scan for arrays whose first element looks like a conversation entry
      for (const key of Object.keys(obj)) {
        try {
          const val = obj[key];
          if (
            Array.isArray(val) &&
            val.length > 0 &&
            typeof val[0] === 'object'
          ) {
            const first = val[0];
            if (first && 'speaker' in first && 'message' in first) return true;
          }
          if (typeof val === 'object' && val !== null) {
            if (hasConversationInObject(val, depth + 1)) return true;
          }
        } catch (e) {
          // ignore and continue
        }
      }

      return false;
    },
    []
  );

  const isFromAmbientListening =
    hasConversationInObject(data) ||
    !!(data as any)?.recordingDuration ||
    (data as any)?.source === 'ambient' ||
    !!(data as any)?.isAmbientForm;

  const isAmbientForm = isFromAmbientListening;

  const methods = useForm<Questionnaire>({
    defaultValues: data,
    mode: 'onChange',
  });

  // Use local state for displayed question groups so it matches transformed form values
  const [displayQuestions, setDisplayQuestions] = useState<any[]>(
    data?.questions ?? []
  );
  const formFields = useMemo(() => displayQuestions || [], [displayQuestions]);

  useEffect(() => {
    if (!data) return;

    const isNoInformation = (value: any): boolean => {
      if (typeof value !== 'string') return false;
      const lowerValue = value.toLowerCase().trim();
      return (
        lowerValue === 'no information found' ||
        lowerValue === 'no information' ||
        lowerValue === 'not found' ||
        lowerValue === 'no data' ||
        lowerValue === 'unknown'
      );
    };

    // Transform data, marking empty or "no information" fields
    // Also dedupe/merge duplicate question groups (some records include duplicates)
    const transformedQuestions = [] as any[];
    const groupIndexByKey: Record<string, number> = {};

    (data.questions || []).forEach((questionGroup: any) => {
      // Only dedupe/merge when a stable id is present. If no id, keep groups as-is.
      const key = questionGroup.id ?? null;
      const processedFields = (questionGroup.fields || []).map((field: any) => {
        const fieldValue = field?.value;
        const isEmpty =
          fieldValue === undefined ||
          fieldValue === '' ||
          fieldValue === null ||
          isNoInformation(fieldValue);
        return {
          ...field,
          value: isEmpty ? '' : fieldValue,
          isEmpty,
        };
      });

      if (key && key in groupIndexByKey) {
        // Merge with existing group: prefer non-empty field values
        const existing = transformedQuestions[groupIndexByKey[key]];
        const mergedFields: any[] = [];
        const existingById: Record<string, any> = {};
        (existing.fields || []).forEach(
          (f: any) => (existingById[f.id || f.label] = f)
        );
        processedFields.forEach((f: any) => {
          const idKey = f.id || f.label;
          const prev = existingById[idKey];
          if (!prev) {
            mergedFields.push(f);
            existingById[idKey] = f;
          } else {
            // choose the field with non-empty value if available
            const chosen =
              (!prev.value && f.value) || (prev.value && !f.value)
                ? f.value
                  ? f
                  : prev
                : prev;
            mergedFields.push(chosen);
            existingById[idKey] = chosen;
          }
        });
        // Replace existing fields with merged list
        existing.fields = mergedFields;
      } else {
        groupIndexByKey[key] = transformedQuestions.length;
        transformedQuestions.push({
          ...questionGroup,
          fields: processedFields,
        });
      }
    });

    const transformedData = { ...data, questions: transformedQuestions };

    methods.reset(transformedData);
    // Update displayed questions so the UI uses the transformed/merged groups
    setDisplayQuestions(transformedData.questions || []);
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <FoodIntakePatternForm
        formFields={formFields}
        readonly
        isAmbientForm={isAmbientForm}
      />
    </FormProvider>
  );
};

export default memo(FoodIntakePatternTimelineForm);
