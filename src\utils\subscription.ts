import dayjs from 'dayjs';

import { routes } from '@/constants/routes';

export interface SubscriptionUserData {
  name: string;
  email: string;
  signupCompleted: boolean;
  establishment?: string;
  role?: string;
  roleId?: string;
  selectedPlan?: string;
  subscriptionCompleted?: boolean;
  quoteRequest?: {
    name: string;
    email: string;
    phone: string;
    organisation: string;
    designation: string;
    requirements: string;
  };
}

// API Response Types
export interface OrganizationPlan {
  id: string;
  planName: string;
  description: string;
  validity: 'Monthly' | 'Yearly' | 'Both';
  features: {
    MRD: Feature[];
    EMR: Feature[];
    Billing: Feature[];
  };
  addOnFeatures: {
    MRD: Feature[];
    EMR: Feature[];
    Billing: Feature[];
  };
  includedModules: string[];
  monthlyTotal: number;
  yearlyTotal: number;
  totalMonthlyBasicAmount?: number;
  totalYearlyBasicAmount?: number;
  isActive: boolean;
  created_by: string;
  created_on: string;
  updated_on: string;
  updated_by: string;
}

export interface Feature {
  featureId: string;
  featureName: string;
  description: string;
  monthlyAmount: number;
  yearlyAmount: number;
}

export interface SubscriptionPlansResponse {
  plans: OrganizationPlan[];
  count: number;
}

export interface QuoteRequest {
  name: string;
  email: string;
  phoneNumber: string;
  organizationName: string;
  designation: string;
  requirements: string;
}

export interface SubscriptionFeatures {
  subscriptionId: string;
  subscriberId: string;
  planId: string;
  planName: string;
  status: string;
  features: Array<{
    id: string;
    featureName: string;
    description: string;
    type: string;
    permissionKeys: string[];
    isBaseFeature: boolean;
    moduleType: string;
    subType?: string;
  }>;
  permissionKeys: string[];
  featuresByModule: {
    MRD: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
    EMR: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
    Billing: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
  };
}

export const SUBSCRIPTION_STORAGE_KEY = 'subscription_user_data';

/**
 * Get subscription user data from localStorage
 */
export const getSubscriptionUserData = (): SubscriptionUserData | null => {
  if (typeof window === 'undefined') return null;

  try {
    const data = localStorage.getItem(SUBSCRIPTION_STORAGE_KEY);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error parsing subscription user data:', error);
    return null;
  }
};

/**
 * Save subscription user data to localStorage
 */
export const saveSubscriptionUserData = (
  data: Partial<SubscriptionUserData>
): void => {
  if (typeof window === 'undefined') return;

  try {
    const existingData = getSubscriptionUserData() || {};
    const updatedData = { ...existingData, ...data };
    localStorage.setItem(SUBSCRIPTION_STORAGE_KEY, JSON.stringify(updatedData));
  } catch (error) {
    console.error('Error saving subscription user data:', error);
  }
};

/**
 * Clear subscription user data from localStorage
 */
export const clearSubscriptionUserData = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(SUBSCRIPTION_STORAGE_KEY);
};

/**
 * Check if user has completed subscription flow
 */
export const hasCompletedSubscription = (): boolean => {
  const userData = getSubscriptionUserData();
  return userData?.subscriptionCompleted === true;
};

/**
 * Get the next step in subscription flow based on current progress
 */
export const getNextSubscriptionStep = (): string => {
  const userData = getSubscriptionUserData();

  if (!userData || !userData.signupCompleted) {
    return routes.SUBSCRIPTION_SIGNUP;
  }

  if (!userData.establishment) {
    return routes.SUBSCRIPTION_SELECT_ESTABLISHMENT;
  }

  if (!userData.role) {
    return routes.SUBSCRIPTION_SELECT_ROLE;
  }

  if (!userData.selectedPlan || !userData.subscriptionCompleted) {
    // Check establishment type to determine next step
    if (userData.establishment === 'hospital') {
      return routes.SUBSCRIPTION_CUSTOM_PRICING;
    } else {
      return routes.SUBSCRIPTION_PLAN;
    }
  }

  // Subscription completed, redirect to main app
  return routes.LOGIN;
};

/**
 * Check if current path is part of subscription flow
 */
export const isSubscriptionPath = (pathname: string): boolean => {
  const subscriptionPaths = [
    routes.SUBSCRIPTION_SIGNUP,
    routes.SUBSCRIPTION_SELECT_ESTABLISHMENT,
    routes.SUBSCRIPTION_SELECT_ROLE,
    routes.SUBSCRIPTION_PLAN,
    routes.SUBSCRIPTION_CUSTOM_PRICING,
  ];

  return subscriptionPaths.some((path) => pathname.startsWith(path));
};

/**
 * Validate subscription step access
 */
export const canAccessSubscriptionStep = (pathname: string): boolean => {
  const userData = getSubscriptionUserData();

  // Allow access to signup page always
  if (pathname === routes.SUBSCRIPTION_SIGNUP) {
    return true;
  }

  // For other steps, check if previous steps are completed
  if (pathname === routes.SUBSCRIPTION_SELECT_ESTABLISHMENT) {
    return userData?.signupCompleted === true;
  }

  if (pathname === routes.SUBSCRIPTION_SELECT_ROLE) {
    return userData?.signupCompleted === true && !!userData?.establishment;
  }

  if (pathname === routes.SUBSCRIPTION_PLAN) {
    return (
      userData?.signupCompleted === true &&
      !!userData?.establishment &&
      !!userData?.role
    );
  }

  if (pathname === routes.SUBSCRIPTION_CUSTOM_PRICING) {
    return (
      userData?.signupCompleted === true &&
      !!userData?.establishment &&
      !!userData?.role &&
      userData?.establishment === 'hospital'
    );
  }

  return false;
};

/**
 * Calculate total amount including 18% tax
 * @param baseAmount - The base amount before tax
 * @returns Total amount including 18% tax (not rounded)
 */
export const calculateTotalWithTax = (baseAmount: number): number => {
  if (!baseAmount || baseAmount <= 0) return 0;

  const taxRate = 0.18; // 18% tax
  const totalAmount = baseAmount + baseAmount * taxRate;

  return totalAmount;
};

/**
 * Format currency for display
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number): string => {
  return Math.floor(amount).toLocaleString('en-IN');
};

export interface ProratedBillingInfo {
  totalDays: number;
  remainingDays: number;
  usedDays: number;
  paidAmount: number;
  dailyRate: number;
  usedAmount: number;
  remainingAmount: number;
  upgradePlanPrice: number;
  proratedPremiumAmount: number;
  currentPlanName: string;
  upgradePlanName: string;
}

export interface Plan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  subtitle: string;
  features: string[];
  popular?: boolean;
  buttonText: string;
  buttonColor: string;
  savings?: string;
  hasAddOnFeatures?: boolean;
  isOrganizationPlan?: boolean;
  apiPlan?: any;
}

/**
 * Calculate prorated billing information for subscription upgrades
 */
export const getProratedBillingInfo = (
  isFromProfile: boolean,
  isDifferentPlanOrBilling: boolean,
  subscriberData: any,
  billingCycle: 'monthly' | 'yearly',
  plan: Plan,
  isShowingUpgradePlan: boolean,
  calculatedNextPlan: Plan | null
): ProratedBillingInfo | null => {
  if (
    !isFromProfile ||
    !isDifferentPlanOrBilling ||
    !subscriberData?.activeSubscription
  ) {
    return null;
  }

  const currentSubscription = subscriberData.activeSubscription;
  const now = new Date();
  const endDate = new Date(currentSubscription.endDate);

  // Calculate total days and remaining days
  let totalDays: number;
  if (currentSubscription.billingType === 'monthly') {
    // For monthly billing, use actual days in the current month
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    totalDays = new Date(year, month + 1, 0).getDate(); // Gets days in current month
  } else {
    totalDays = 365;
  }

  const remainingDays = Math.max(
    0,
    Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  );
  // If remaining days exceed total days (new subscription), used days should be 0
  const usedDays =
    remainingDays > totalDays ? 0 : Math.max(0, totalDays - remainingDays);

  // Use amount with tax for prorated calculations (what user actually paid)
  const baseAmount = currentSubscription.totalAmount || 0;
  const paidAmount = calculateTotalWithTax(baseAmount);

  // Calculate daily rate based on amount with tax
  const dailyRate = paidAmount / totalDays;

  // Calculate used amount and remaining amount
  const usedAmount = dailyRate * usedDays;
  const remainingAmount = dailyRate * remainingDays;

  // Calculate upgrade plan price
  const upgradePlanPrice =
    isShowingUpgradePlan && calculatedNextPlan
      ? billingCycle === 'monthly'
        ? (calculatedNextPlan.apiPlan?.totalMonthlyBasicAmount ??
          calculatedNextPlan.monthlyPrice)
        : (calculatedNextPlan.apiPlan?.totalYearlyBasicAmount ??
          calculatedNextPlan.yearlyPrice)
      : billingCycle === 'monthly'
        ? (plan.apiPlan?.totalMonthlyBasicAmount ?? plan.monthlyPrice)
        : (plan.apiPlan?.totalYearlyBasicAmount ?? plan.yearlyPrice);

  const proratedPremiumAmount = Math.max(0, upgradePlanPrice - remainingAmount);

  return {
    totalDays,
    remainingDays,
    usedDays,
    paidAmount,
    dailyRate,
    usedAmount,
    remainingAmount,
    upgradePlanPrice,
    proratedPremiumAmount,
    currentPlanName: currentSubscription.planName,
    upgradePlanName:
      isShowingUpgradePlan && calculatedNextPlan
        ? calculatedNextPlan.name
        : plan.name,
  };
};

/**
 * Get formatted expiry date text based on billing type and days until expiry
 * @param endDate - The subscription end date
 * @param billingType - 'monthly' or 'yearly'
 * @returns Formatted expiry text
 */
export const getFormattedExpiryText = (
  endDate: string,
  billingType: 'monthly' | 'yearly'
): string => {
  const today = dayjs().startOf('day');
  const expiryDate = dayjs(endDate).startOf('day');
  const daysUntil = expiryDate.diff(today, 'day');

  if (billingType === 'yearly') {
    // For yearly billing: always show expiry info
    if (daysUntil < 0) {
      const formattedDate = new Date(endDate).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
      return `Expired on ${formattedDate}`;
    } else if (daysUntil <= 30) {
      // Within 1 month, show days remaining
      if (daysUntil === 0) {
        return 'Expiring today';
      } else if (daysUntil === 1) {
        return 'Expiring in 1 day';
      } else {
        return `Expiring in ${daysUntil} days`;
      }
    } else {
      // More than 1 month away, show expiry date
      const formattedDate = new Date(endDate).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
      return `Expires on ${formattedDate}`;
    }
  } else {
    // For monthly billing: show date format for plans > 30 days
    if (daysUntil < 0) {
      const formattedDate = new Date(endDate).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
      return `Expired on ${formattedDate}`;
    } else if (daysUntil <= 30) {
      // Within 1 month, show days remaining
      if (daysUntil === 0) {
        return 'Expiring today';
      } else if (daysUntil === 1) {
        return 'Expiring in 1 day';
      } else {
        return `Expiring in ${daysUntil} days`;
      }
    } else {
      // More than 1 month away, show expiry date
      const formattedDate = new Date(endDate).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
      return `Expires on ${formattedDate}`;
    }
  }
};

/**
 * Check if a menu item or feature should be hidden based on subscription features
 * Hides items where user lacks permission but subscription features include those permission keys
 * Applies for normal accountType, with or without subscriberId
 */
export const shouldHideBasedOnSubscription = (
  permissions: string[],
  subscriptionFeatures: SubscriptionFeatures | null,
  data: { accountType?: string; subscriberId?: string },
  requiredPermissions: string[],
  module?: 'mrd' | 'emr',
  requireAll: boolean = false
): boolean => {
  // Only apply for MRD/EMR modules
  if (module && module !== 'mrd' && module !== 'emr') {
    return false;
  }

  // If accountType is normal and subscriberId is null, check permissions
  if (
    data?.accountType === 'normal' &&
    !data?.subscriberId &&
    requiredPermissions &&
    requiredPermissions.length > 0
  ) {
    if (requireAll) {
      // Require all permissions
      const userHasAll = requiredPermissions.every((perm) =>
        permissions.includes(perm)
      );
      if (!userHasAll) {
        return true; // Hide if user doesn't have all required permissions
      }
    } else {
      // Require any permission
      const userHasAny = permissions.some((perm) =>
        requiredPermissions.includes(perm)
      );
      if (!userHasAny) {
        return true; // Hide if user doesn't have any required permission
      }
    }
  }

  // Check conditions: normal accountType, subscriberId, subscriptionFeatures, requiredPermissions
  if (
    data?.accountType === 'normal' &&
    data?.subscriberId &&
    subscriptionFeatures &&
    requiredPermissions &&
    requiredPermissions.length > 0
  ) {
    const subscriptionPermissionKeys =
      subscriptionFeatures.permissionKeys || [];

    if (requireAll) {
      // Require all permissions
      const userHasAll = requiredPermissions.every((perm) =>
        permissions.includes(perm)
      );
      if (userHasAll) {
        return false; // Don't hide
      }
      // Hide if subscription has all required permissions
      const subscriptionHasAll = requiredPermissions.every((perm) =>
        subscriptionPermissionKeys.includes(perm)
      );
      return subscriptionHasAll;
    } else {
      // Require any permission
      const userHasAny = permissions.some((perm) =>
        requiredPermissions.includes(perm)
      );
      if (userHasAny) {
        return false; // Don't hide
      }
      // Hide if subscription has any required permission
      return requiredPermissions.some((perm) =>
        subscriptionPermissionKeys.includes(perm)
      );
    }
  }

  return false;
};
